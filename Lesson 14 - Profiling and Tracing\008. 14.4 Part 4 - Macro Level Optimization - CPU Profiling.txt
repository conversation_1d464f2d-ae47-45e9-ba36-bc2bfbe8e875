Now, let me show you how to get a CPU profile from your running Go program. This is a little different from heap profiles because heap data is historically already there. When we want to get a CPU profile, we actually have to be running load against the program. The default amount of time a CPU profile takes to gather statistics on the running program is 30 seconds. You need to be mindful of any timeouts you have on your endpoints. If your server is configured to time out in less than 30 seconds, your profiling might start to fail. But we can adjust the duration—I'm going to do that.

I've got my program already running, just like we were during the heap profiling. Now, I'm going to put load on the server again, aiming for at least 10 seconds of sustained activity. To achieve that, I'll run about a million requests—I can kill it later if needed. In this terminal tab, I'm going to use the Go tool pprof again. Remember, no switches are needed for CPU profiles. We still have the endpoint called "profile." If I go back to the browser window, you can see there's a link labeled "profile." This is the raw endpoint.

Here's what often happens: by default, this profile endpoint tries to run for 30 seconds. So when I click it, it starts a 30-second profiling session. The only way to control this properly in the browser is to specify the duration upfront using a query parameter—say, seconds=5. That way, it only runs for five seconds. Hopefully, it returns the result in the browser window. But I forgot to include debug=1. (laughs) Look at what I did—just "<PERSON>" and then debug=1. That actually causes it to write out the file.

Let me try that again. I'll bring up the URL like this: profile?seconds=2&debug=1. Maybe this time we'll get some output. Let's see. Nope, it didn't display anything, but it did download the file. If I look down here, it downloaded it anyway, even with debug=1. That's something I'll need to experiment with. But this is the profile link we're going to use.

So here's the plan: we'll put some load on the server, and then I'll hit this endpoint. I want it to gather 10 seconds of profile data. I think the parameter is "seconds"—let me double-check. For the profile endpoint, it's seconds, and I believe it's case-sensitive. I should probably use lowercase "s." So I'll say seconds=10 and go from there.

Let's put some load on the server. Okay, we know it's running—there it is. Now I'll request 10 seconds of profile data. That didn't work as expected, so let's try again. Debug pprof profile—if it doesn't work, we'll adjust. There it goes. Fetching for 10 seconds. I hope "seconds" is plural; if not, we'll fix it. Honestly, I'm learning some of this alongside you, especially since we're using Go 1.11 Beta. And look, I'm not trying to appear perfect here. Life isn't perfect, so you're seeing the real process unfold.

At this point, I think we've already gone past 10 seconds. It's doing a full 30-second profile. I probably should have used lowercase "s" in the parameter. But it's running now, so let's let it finish. It ran for 30 seconds and didn't respect my intended duration—the parameter should have been lowercase "s" after "seconds." No big deal. I had enough load going through the system that the profile was still useful. My fan just kicked in from the CPU usage.

Now I'll kill the load because I've got the profile I need. Look at what I did: I went to pprof, gave it the profile endpoint, and wished I had used lowercase "s" for the seconds parameter. I didn't, but that's okay—we still got a full 30-second profile.

Now I can do the same things I did before. Let me get the top 40 cumulative functions that are taking the longest to run. Not surprisingly, we see connection-related functions, and "server" should be up there too since everything starts there. Other than that, it's mostly standard library functions, which makes sense—this program appears to be running pretty efficiently.

But the point is, I can now examine specific functions. For example, I can say "list execute template" to look into that function. We should be able to find it. You can see where that 37 seconds is being spent—it's in standard library code. I don't particularly care about optimizing that, but the idea is clear: this profile endpoint gives you the ability to take a live CPU snapshot of your server while it's running.

This allows us to start identifying where the program is spending the most time. If you're having production issues, this is a powerful way to begin diagnosing where the bottlenecks are in your code and where you might focus your optimization efforts. All of this data is right here, accessible and actionable.

And again, this is also the only place where you can see the current number of goroutines in your Go program. All of this information is raw data that the profiling tools use to generate their reports and visualizations.

All right, so now we've seen how to perform a CPU profile on a running Go program—as long as you've bound the debug/pprof endpoints into your server.