What I want to show you now is how to use the execution tracer to help find and identify potential performance problems. A profiler is great—it can show you what's happening—but sometimes what you need to see is not what's happening, but what's *not* happening. The tracer can give us both.

Let's start with this basic program and walk through how all of this could be used. One day, I wanted to write a function whose entire purpose was to search through *n* number of documents—specifically RSS feed documents—and count how many times a given topic appears in each document. It's a simple find function. There's a global variable `found` initialized to zero. We range over all the documents—we don't know how many we'll get; it could be a thousand, it could be a million—and then we perform four steps: open a file, read the entire contents into memory (since it's XML, we need the whole thing), unmarshal it into a local variable `document`, and then search that document—the RSS feed—for the term, incrementing a local counter. It's not very complicated: four stages, and we get a count.

I wrote a main program. Right now, it searches 1,000 documents. We'll pretend this is a representative number, but we have to think about scale—what happens if there are a million documents? For now, we're working with 1,000. We're searching for the term "president," calling our `find` function, getting back a number, and writing it to standard output.

I'm already in this directory. Let's build it, and then use the `time` function to see how long it takes. When I run this, it took 821 milliseconds to find the term "president" 7,000 times across those 1,000 documents. We know it will take longer with more documents, but this is our baseline.

Now, let's say we know this isn't fast enough and we want to make it run faster. We need to leverage our tooling again—specifically, profiling. Let's start with profiling and see if it can help us make this program faster.

Another way to use the profiler, which I haven't shown yet, is through the standard library. The `pprof` package is part of the runtime. We can call `StartCPUProfile`, write it to standard output, redirect the profile on the command line, and make sure to call `StopCPUProfile` to flush and finalize everything stored in memory. So: start CPU profile, stop CPU profile—simple. Let's save, build, and run it again. This time, I redirect the profile output.

Notice it took longer to run. That makes sense. Remember, profiling means the program is hooked into the OS. At regular intervals, the OS interrupts the program, collects program counters, and resumes execution. This overhead slows the program down. I don't care about that—I care that I now have a `p.out` file.

What can we do with profiles? Run `go tool pprof p.out`. This is a CPU profile. I'm now in the pprof interface. I already know about the `find` function—this is micro-level optimization, like benchmarking. So I run `list find`. A couple of functions named `find` appear—fine. I locate my `find` function. Out of 760 milliseconds of cumulative time, almost all of it was spent on the `open` file call. If we want to speed up this program, algorithm, or implementation, we must focus on `open`.

If I generate a call graph, that red section shows `open` coming down through the stack. I could isolate it with `web list find`, but I didn't. Ultimately, we learn that if I want to make this program faster, I need a new operating system—the syscalls are killing me. But we're not replacing our OS anytime soon. So here's a case where the profiler *isn't* helping us optimize. Focusing on anything else would be a waste of time. This isn't helping. Let's clear it out.

Let's try something else. If profiles aren't helping, let's run a trace.

There are many ways to run traces: command line, `-trace` in benchmarks, etc. But we'll use the standard library: `trace.Start`, write to standard output, and `trace.Stop`. Let's build and run again, this time redirecting to `t.out`.

Notice the performance is back—because tracing doesn't stop the program. Instead, it logs every function call in and out, down to the microsecond. It collects a tremendous amount of data, but without significantly slowing the program.

Let's examine the trace: `go tool trace t.out`. I'm on Go 1.11 beta, so this includes new features we can't fully explore yet—like tagging, regions, tasks, and annotations—which will make tracing even more powerful in the future.

Clicking the first link, we see various informational views. One of the most powerful is the *goroutine trace*. Right now, we had only one goroutine, but we get detailed info: how many microseconds of GC sweeping, GC pause times—12 milliseconds over the full 838. So much information about a single goroutine. We can dive into that later. First, let's look at the larger trace.

This view shows the trace from start to finish. We can select a block and see it took 844 milliseconds—we already knew that. We can zoom and inspect. The top line shows the number of goroutines. We know this is a single-threaded goroutine program, yet at some points, more goroutines appear.

Let me zoom in—holding Alt and scrolling the mouse. You can see extra goroutines appeared. The next line shows the heap. The colors change between Go versions; right now, we have a brownish color and light green. The light green is the *live heap*—the actual memory in use. Clicking on it, the heap is around 4 MB, which makes sense.

The live heap grows, then drops, grows again, drops—this pattern aligns with garbage collection, shown in light blue. Looking deeper, the live heap drops completely after GC finishes. So we know: the live heap grows to capacity, GC runs, reduces it, and the cycle repeats. This also explains the extra goroutines—GC uses goroutines, so they appear during collection and disappear afterward.

I can click on one—there, three goroutines running. Once GC finishes, they're no longer in a runnable or executing state. Interesting.

We also see how much CPU GC is using. I can see total runtime and CPU load—lines going all the way down. I can even see different GC phases by zooming further.

Our algorithm runs on G1. You can see G1 pauses briefly and resumes. We can measure that latency—about 35 milliseconds where no work was done. This is like "stop-the-world" time for our goroutine—we're not making progress. Interesting.

During that time, GC marking work happens on P1. Lines under G1 represent system calls. When a goroutine is context-switched or paused, you see blocks of system calls. There's also a slight pause in this goroutine—we can inspect the stack trace right before that pause. Zooming out a bit, we see it was on `find`, line 74.

From this view alone: the trace starts, another goroutine spawns. There's a "Flow events" option showing where goroutines are created and resume—cool. But what's most interesting is this: at a macro level, our goroutine starts on P0. A GC happens. Digging in, G1 gets rescheduled—balancing across P2, P1, back to P0, etc. After the next GC, it's on P2, then P0 again. You can see how GC introduces scheduling chaos.

But here's the key insight: we can now see where our performance problems come from—something the profiler couldn't tell us. Where is the bottleneck? It's that we're only using *one P at a time*. Watch—I'm tracing with my mouse: only one processor core is active at any moment. We don't have much stop-the-world time, but if we could use all eight cores, this should run much faster. Our goal should be to fully utilize CPU capacity, not just for GC, but for our actual work.

So let's make code changes to engage more CPU capacity.

First, examine the algorithm: for each document, we open, read, unmarshal, and search. I like solving problems with one goroutine first—it clarifies the workflow. Then we can distribute the work. This is a distribution problem.

One approach: a *fan-out* pattern. Since we have many independent documents, why not assign each to a goroutine and let the scheduler handle concurrency? "You're smarter than I am—handle it as efficiently as possible." So let's try fan-out across the 1,000 documents and see if performance improves. Then consider further optimizations.

We have the global `found`. First, determine how many goroutines to create. Let's create one per document. We'll need orchestration—so we use a `WaitGroup`. Initialize it, add `g` goroutines. We want `find` to return so all goroutines can sum their counts into `found`. Use `WaitGroup` for orchestration: add `g`, then at the end, `Wait()`—hold the function until all goroutines report completion. Great.

Now, create a goroutine for every file. Use a literal function: `go func()`. Now we have a goroutine per document—but issues arise. The `return` statements no longer work—literal functions can't return. That's fine—we'll remove them. Now the linter complains: closure bug on `document`. If left as-is, all goroutines process the same document. So we must pass `document` into each goroutine to fix the closure.

Now we have a clean per-goroutine solution. But orchestration isn't complete—we must call `Done()` so each goroutine signals completion. Done.

But another problem: `found`. It's global, and every goroutine does `found++`. With eight cores, this creates a *data race*—eight goroutines incrementing the same memory location simultaneously.

Let me whiteboard this. I'm creating 1,000 goroutines (one per document), but I'm on an 8-core machine. The `found` variable is shared. Each goroutine, at line 89 or 94, wants to perform a read-modify-write. Without synchronization, their writes can overwrite each other. This is a serious data race.

To fix it, use the `atomic` package: `atomic.AddInt32(&found, 1)`. We don't need a large counter, so `int32` is fine. This fixes the race at the hardware level. Now, when goroutines want to increment `found`, they line up—only one operates at a time. Atomic instructions ensure correctness.

Great—no data races, one goroutine per file. Should run fine. But I'm thinking about scale—and something scares me. What if we process a *million* documents? That's a million goroutines. The scheduler and system may not handle that. We need fewer goroutines—less is more.

So instead: since we have eight cores, use only eight goroutines. Feed them all the work as they complete it. If this works, it doesn't matter if it's 1,000 or a million documents—the cost is eight goroutines.

Let's modify the program. Now `g` is `runtime.NumCPU()`—eight goroutines. All orchestration code is good, but we need to feed work to these eight. Use a *buffered channel*.

Create a `chan string`—file names are strings. Buffer size: `len(docs)`. Use a loop to pre-fill the channel with all document names, then close it. Why close? Once the channel is drained, it signals "done" without data—goroutines terminate when no work remains. Beautiful: push all work upfront, close, and let goroutines drain it. No latency cost—filling a million items is fine.

Now, the loop creating goroutines runs `g` times—eight times. `i < g`, `i++`. We no longer pass `doc`—work comes from the channel. Replace the goroutine body: use `for doc := range ch` to receive from the channel. All eight goroutines loop, receive the next document, process it (open, read, unmarshal, search), and repeat. When the channel is closed and empty, the loop ends, goroutines exit, `Done()` is called.

Think about cache coherency. We now use a local `lFound` variable per goroutine. Only eight goroutines, so each uses its own stack, its own cache line. Increments don't thrash memory. Only at the end do we write back: `atomic.AddInt32(&found, lFound)` in a `defer`.

We've reduced memory thrashing from 7,000 atomic operations (once per match) to just eight (once per goroutine). We gain mechanical sympathy—better cache behavior. We're not even using atomics during the loop—only at the end.

This algorithm is more efficient: fewer goroutines, better scheduler behavior, scales to a million documents, better cache usage. We only do eight atomic operations—in the `defer`.

I'm excited about this.

Over 1,000 documents: previously ~322 ms. Let's build: `go build`. Now it's around 280 ms—shaved off 40–50 ms. Imagine the savings over a million.

Let's check the trace: `go tool trace t.out`, view trace. Looks clean—uses full CPU capacity. Compared to the old trace, we now see eight goroutines working in parallel. Zooming in, it's even cleaner—only eight to analyze.

With new tracing APIs for regions and tasks, we could break down what each goroutine does. On the previous version (one goroutine per document), we could've done goroutine analysis—though it's not running now. But here, `n=8`—you see eight goroutines, how execution is distributed, GC pause times. You can drill into any one and see its trace. Isolate behavior per goroutine. With regions and tasks, it gets even better.

This tooling keeps improving with every Go version. I'm glad to share this on 1.11 beta—by the time you see this, 1.11 will be stable, 1.12 around the corner. The UI changes since 1.10 are significant.

I've shown how to do micro-optimizations not just with profilers, but with tracing. We did live coding to demonstrate how concurrency primitives—channels, WaitGroups—simplify orchestration. One of the nicest things: we don't have to micromanage work distribution. That's the beauty of the Go scheduler. We throw work at it efficiently, then wash our hands. The runtime handles the rest.

This is tracing in Go.