Now that we've learned how the stack works in Go, our next step is to understand the heap a little bit more and really dive into garbage collection. Remember, I told you that performance will be impacted by two main factors: latency from network and disk I/O, and second, allocations and the garbage collector. So let's learn about the mechanics of the garbage collector and discuss some guidelines we should follow as we deepen our understanding of how memory works and start writing more code.

The key thing about Go's garbage collector right now is that it's a tri-color mark-and-sweep concurrent collector. One important detail is that it's not a compacting garbage collector—memory on the heap does not move around, which is interesting because memory on the stacks potentially can. Once an allocation is made on the heap, it stays there until it gets swept away. The "tri-color" part refers to the mechanism we'll discuss, and the fact that it runs concurrently is also very important. This was something the Go community asked the language designers for a long time: a collector that allows goroutines to run at the same time as memory is being collected. They were able to deliver that. But again, nothing is free. One of the costs of having a concurrent collector is that there are still some stop-the-world pauses. The goal, however, is to reduce these stop-the-world events to their absolute minimum.

Everything begins and ends with the pacing algorithm. The garbage collector uses this algorithm to balance three things—and it's quite impressive. I'm not a PhD in this, and there's a lot of documentation if you want to go deep, but the algorithm tries to balance three goals in Go. First, we want the smallest possible heap size—minimizing memory usage. How do we achieve the smallest heap size while allowing the garbage collector to run at a reasonable pace? Second, we want stop-the-world latency to never exceed 100 microseconds per GC cycle. Note that the total garbage collection time may be longer, but we don't care as much about the concurrent phases—we care about the stop-the-world time. Third, there's a cost to achieving this: the garbage collector is allowed to use up to 25% of your available CPU capacity. It doesn't necessarily have to, but it's permitted to, depending on what's happening in your application. This system is very complex, so let's go over it again.

How do we maintain the smallest heap size, run at a reasonable pace, keep stop-the-world latency under 100 microseconds, and use no more than 25% of available CPU? Let's talk about that CPU cost for a moment. This is a bit more complex than what I'm about to illustrate, but it gives you a general idea of where that 25% might come from. Imagine you're on a machine with four cores, meaning you have four Ps (processors), and thus a four-threaded Go program, since we can run one thread per core. Now suppose you're running a web service with 10,000 goroutines distributed across all your Ps—your execution contexts—and they're all doing work. Where could the 25% CPU usage come from?

The garbage collector itself uses Go's heap. Remember, Go is written in Go—the runtime and compiler are too—so the garbage collector uses goroutines and the same infrastructure to get its work done. One possible scenario—and this is simplified, especially when we get into tracing—is that the garbage collector might claim an entire P for itself. That means all other goroutines are distributed across the remaining Ps, and all garbage collection work is done using that one P. That would consume 25% of your total CPU capacity. But even then, you could still run three goroutines in parallel while garbage collection is ongoing. That's the general idea behind that constraint.

Different garbage collectors have different trade-offs. Some prioritize high performance to finish quickly, but Go's approach is focused on low latency—running consistently and steadily alongside your application. So, with that 25% CPU allowance, when the pacing algorithm decides to run, it looks at two things: the current size of the heap and the live heap.

Let's define those. Suppose you start your program and Go determines that a 4MB heap is sufficient. That's not unreasonable—heap sizes are often in the megabyte range. In this 4MB heap, imagine 2MB of memory is occupied by long-lived data—perhaps a map used as a cache, or some persistent state. That memory isn't going anywhere. That means the live heap—the amount of memory currently in use and reachable—is 2MB. This is what the pacing algorithm works with: the total heap size and the live heap size. Its goal is to maintain the smallest possible heap at a reasonable pace, ensuring stop-the-world latency stays at or below 100 microseconds.

As your program runs, the live heap grows and moves closer to the top of the heap. At some point, if it gets too close, we need to bring it back down. We can't let the live heap reach the full heap size, because if it does and we're running garbage collection concurrently, we risk exceeding the heap capacity. To prevent that, Go has a configuration option called GOGC. The default value is 100, which means the heap is allowed to grow by 100% relative to the live heap before a collection is triggered. So if the live heap is 2MB, collection starts when the heap reaches 4MB.

We want to avoid letting the live heap get too close to the heap limit, so we aim for consistent allocation patterns. This allows the pacing algorithm to maintain balance. It tries to balance all three factors: heap size, latency, and CPU usage. Ideally, the live heap approaches the heap limit, the pacing algorithm triggers a collection, and the heap usage comes back down. This might happen on a cycle of one or two milliseconds—perfectly reasonable. Think about it: if you're running garbage collection every two milliseconds, but only pausing the world for 100 microseconds each time, that's a good trade-off. The stop-the-world time might only account for 5% to 10% of the total GC time. That's acceptable. We get to run concurrently most of the time.

The pacing algorithm is designed to achieve this. Another nice feature is that if a goroutine starts behaving poorly during garbage collection—say, running a tight loop—the garbage collector can detect that and stop it. It can context-switch it off a P, give another goroutine time on that thread, and even recruit the misbehaving goroutine to help with garbage collection. That's a powerful and clever mechanism.

Now, looking at the garbage collection process, there's a chart showing the different phases and where stop-the-world pauses occur. You'll notice there are two brief stop-the-world events in every garbage collection cycle. The first is at the beginning, to turn on the write barrier. The second is near the end, before the sweep phase, for cleanup. The first one—enabling the write barrier—should be extremely fast. The second one can take longer, depending on the workload.

The write barrier is essential. Its purpose is to ensure that goroutines running during the mark phase report their memory writes so the garbage collector can stay informed. I like to think of the write barrier as a reporting mechanism that keeps the scheduler and garbage collector aware of memory changes. To enable the write barrier, we must bring each P to a safe point. That means stopping every goroutine briefly to set a flag. In Go 1.10 and earlier—though at the time of filming, 1.11 is in beta—the only way to bring a P to a safe point is to wait for a goroutine to make a function call. Scheduling in Go is cooperative, not preemptive, and cooperation happens primarily during function calls. So if a goroutine is in a tight loop—say, calculating pi to a high precision and executing raw CPU instructions without making function calls—it can stall garbage collection. Other Ps might reach safe points, but we're stuck waiting for that one P.

This is why it's important in Go that CPU-intensive work, like heavy math, includes periodic function calls. Recently, there's been work on enabling preemption at the statement level to bring Ps to safe points more reliably. That would be a major improvement, but it's complex due to memory barriers and atomicity requirements. For now, we must ensure goroutines make function calls so the system can function healthily.

Once all Ps reach safe points—this stop-the-world pause is typically very short, maybe 10 microseconds—we enable the write barrier. Then the garbage collector starts using some Ps while the rest continue running application code.

Now, remember: the heap is essentially a large graph. We have stacks, which contain frames, and some of those frames hold pointers to heap-allocated objects. Other heap objects may point to each other. What we have is an object graph, and it all starts and ends with the stacks. You could have global variables pointing into the heap, but we generally avoid global variables due to testability issues, hidden dependencies, and configuration complexity. So let's assume we don't use them. Even if there are a few, the principle remains.

From a tri-color perspective, everything in the graph—stacks and heap objects—starts out white. This is a high-level view; there are detailed articles if you want to dive deeper. Everything is initially marked white. As we scan the stacks, we mark reachable objects. The pointers from the stack are our root references. When we find them, we mark them gray and place them in a queue. Then, in the next phase, we take each gray object, mark it black, and examine what it points to. Any objects it references are marked gray. We repeat this process: pull a gray object, mark it black, mark its children gray. The idea is to iterate through the entire graph until only black (reachable) and white (unreachable) objects remain.

Black objects are kept because they're reachable from a root (like a stack frame). White objects have no references and can be swept away in the next phase. The mark phase runs concurrently with your application. Once marking is complete, there's another brief stop-the-world pause—again, ideally under 100 microseconds—for cleanup. Then the sweep phase reclaims the white objects.

This is what happens under the hood. It's complex, with many details enabling concurrency. But from our perspective as developers, the key takeaway is this: we must balance value and pointer semantics. We should use value semantics as much as possible and understand when to use pointers—knowing the costs and benefits. Our goal is to reduce the number of allocations our programs make.

You won't write zero-allocation software—that's unrealistic. We're not trying to eliminate allocations, but to reduce them. We aim for a healthy balance between value and pointer semantics. Less is more: the fewer allocations, the less work the garbage collector has to do, and the faster everything runs. More allocations mean more work and longer GC cycles. Also, a larger heap doesn't necessarily mean better performance. Even with a small live heap, a larger total heap means more memory to scan when the live heap grows, increasing GC workload.

So we don't tweak configurations like GOGC unless absolutely necessary. We let the pacing algorithm do its job. We write code with consistent allocation patterns, reduce unnecessary allocations, and use profiling and benchmarking to identify and eliminate waste. Most importantly, we learn to maintain a strong balance between value and pointer semantics as we write Go code.