We're about to shift gears and start talking about methods, but what we're really going to focus on is decoupling. Up until now, throughout this video, we've been focused on concrete data, and I've been emphasizing that everything we do is built around concrete data. The problems you solve are based on the concrete data. If you don't understand the data, you don't understand the problem. And if you don't understand the problem, you can't write code. If the problem is changing, that means the data is changing—and when the problem changes, your code must change too. Our work, our daily lives, are built around data. Remember, we're all data scientists.

We've also been showing that your performance comes from the data—everything we do with data affects performance: network latency, disk I/O, memory access latencies. It's all about data, data, data, data, data. But here's the problem: when the data is changing and the problem is changing, we end up changing our code. What we want to do is minimize the cascading changes that ripple through the entire codebase. That's where decoupling comes in.

So far, we've been focusing on the concrete data. Now we need to start dealing with decoupling—how do you decouple? Decoupling is a major part of what we do, and it's achieved through behavior. We now need to start focusing on behavior. Behavior is where we do design, architecture, and decoupling. The real work—the real work, the real work—is down here in the concrete and the data. One of the things we need to learn is to start from the concrete and the data and move upward. I really, really, really don't want you working in the opposite direction. Too many developers do this: they start with behavior, they start with decoupling, they start thinking about abstractions—and they're guessing. If you start up here, you're guessing. I want to eliminate the guesswork. Solve the problem first in the concrete, then move toward decoupling.

I'll show you more of this as we get into the design sections of this class, but for now, I still need to teach you the mechanics—the mechanics of what happens above this line—so we can eventually get into design and architecture.

Alright, let's look at some code. Specifically, we're going to look at how Go allows data to have behavior. Think about that for a moment: allowing data to have behavior. Go has functions—we've been using functions since the beginning—but Go also has methods. A method is a function that has what we call a receiver. Let's look at what a method looks like in Go.

Here we have a user-defined type called `user`, with fields for `name` and `email`. Just above the type definition, you see two methods. I always want methods to come after the type. What makes this a method is a special parameter defined between the `func` keyword and the function name. This is the receiver—it's a parameter, so think of it as such. Because we have this receiver parameter, the function is now considered a method. It's these methods that allow data to have behavior.

One of the things we need to learn, especially as we move beyond mechanics into design, is when a piece of data should have behavior. I want this to be the exception, not the rule. This might be difficult if you're coming from an object-oriented programming language, because OOP encourages you to always associate data with behavior. Object-oriented programming teaches that everything in your program should resemble a real-world object with state and behavior. But I don't think that's the best approach in Go. In fact, Go tends to separate state from behavior most of the time. When we do this, we write less code and things become simpler.

Functions should be your first choice—until they're no longer reasonable or practical. That's when you consider using a method. So I still need to teach you when to make that exception: when should a piece of data have behavior? But for now, let's focus on the mechanics.

Go is interesting in this regard. We've been discussing value and pointer semantics, and normally these don't hit Go developers hard until we get to methods. Now, with methods, you have to make a choice: should you use a value receiver or a pointer receiver? This is a crucial decision, and many Go developers get stuck here. Too many, especially early on, adopt this rule: if the method needs to mutate the data, use a pointer receiver to share the data; if it doesn't need to mutate, use a value receiver and let it operate on a copy. This approach is very, very bad. I cannot stress this enough: we must be consistent with our semantics. The data drives the semantics. Once you choose a semantic model—value or pointer—everything related to that data must follow it. Your methods and your code must respect the semantic model of the data.

If you're familiar with languages like C++, C#, or Java, you've dealt with receivers before—the `this` pointer, the `self` pointer. Those are receivers, specifically pointer receivers, and they're named for you. One of the brilliant things Go does is give you the choice between value and pointer semantics—value receivers and pointer receivers—and it also lets you name the receiver. But we should never use names like `this` or `self`. The receiver name should be short and sweet—never more than three letters. This is the highest level of context in your Go program. For example, I use `u` for `user`, whether it's a value or a pointer to `user`.

So, a value receiver, like in the `notify` method, means the method operates on its own copy when called. A pointer receiver, like in `changeEmail`, means the method has shared access to the data. These are your two choices.

As we're learning mechanics, you'll see code that mixes value and pointer semantics. But once we move into design, you won't see that anymore—no more mixing. But for now, I'm teaching mechanics, so I need both to show you the behavior. Remember: mechanics and semantics teach us about behavior, and that helps us understand how the code will perform.

Now, let's look at `main`. On line 32, I'm constructing a value of type `user` named Bill. Bill is a value of type `user`. Notice the call on line 33 to `changeEmail`. `changeEmail` uses pointer semantics—it expects to be passed a pointer. You might wonder: why does this compile? Bill is a value, not a pointer. This is where Go is clever. When calling a method, Go only cares that the data is of type `user`—it doesn't matter whether it's a value or a pointer. As long as we're working with data of type `user` in some form, Go can adjust to make the call. So when we call `changeEmail` on Bill, Go automatically takes the address of Bill, satisfies the receiver type, and shares Bill with the method.

On line 34, we call `notify`, which uses value semantics. No problem—Bill is already a value, so `notify` operates on its own copy. Value semantics means operating on a copy; pointer semantics means shared access.

Now, on line 38, I'm doing something I told you not to do: I'm using the address operator when constructing a value assigned to a variable. I don't want you to start your variable's life as a pointer. Avoid using pointer semantics at construction when assigning to a variable. That said, I'm showing this to illustrate a point. `changeEmail` already uses pointer semantics, so calling it on `Joan`, which is a pointer, is fine. But on `notify`, which expects a value, Go again steps in: it takes the value that the pointer points to and satisfies the call.

What you're seeing here is Go adjusting to match the receiver type with the available data. But now we're switching semantics in the code—and I've emphasized repeatedly: switching semantics leads to trouble. In fact, the call on line 40 is the worst case. You're not supposed to take a copy of a value that's being shared via a pointer. Not all data can be copied, and if data is shared with you, you should assume it's not safe to copy. If I saw code like line 40 in a code review, it would really concern me.

We want to maintain consistent value semantics. We'll talk more about that, but my goal here was to show you how Go can adjust method calls to match the receiver type based on the data available.

Now, what I want to do next is give you clear guidelines on when to use value semantics and when to use pointer semantics, so we can come back to this code and better understand what we're doing and why.