Well, the last two tests we created—the basic unit test and the table test—were fantastic. We're testing the GET call, which is great. But here's the problem with those two tests: they require a connection to the outside world. We're hitting a live server. Normally, we aren't that lucky in our CI environments when it comes to having access to the outside world.

Now, I always want to be very careful about mocking. I don't want to mock databases—I prefer to use <PERSON><PERSON> for that. For certain systems where it's critical that we know we're talking to live systems or dealing with binary protocols, I want to ensure everything is working correctly. Again, for databases, don't mock databases—use Docker instead. But many things *can* be mocked, and these tests we ran are perfect candidates for mocking.

I don't particularly care about the HTTP protocol details here. I know web servers work. What I care about is this: if I send a request and get back a response, can I process it correctly? So what I want to show you is how you can mock a GET call using tools already built into <PERSON>'s standard library.

What's nice is that Go gives us a package called `httptest`. We're going to use this to mock these GET calls, and we'll use it again later to mock some of our internal components as well.

Look at what we do in this code. Since we're going to be making a GET call to the goinggo.net web server for that RSS feed—which is nothing more than an XML document—I've created a raw string here called `feed`. It's a raw string literal, as you can see by the use of backticks. Those backticks preserve everything exactly as written, including carriage returns, line feeds, tabs, and any other whitespace I include. This is an RSS document that represents at least a portion of what we expect to get back when we hit the goinggo.net web server.

Great. Now I've also created some structs with XML tags so that when we mock the call and get this raw string back, we can unmarshal it into our real data structures and perform actual testing.

But the key to the entire mock is this function right here called `mockServer`. Notice that `mockServer` uses pointer semantics for the `Server` type inside `httptest`. The `Server` type allows us, during unit testing, to stand up a web service on some random port. We'll be able to get back an endpoint and write code that we know executes when the endpoint is called.

Here's the bottom line: when we call `NewServer` here, it stands up a web service on a port that it will tell us about. And when we hit the endpoint it provides—any endpoint—it will execute the function I'm defining right here on line 63.

I like to assign literal functions to variables before passing them in because I find the code more readable. A lot of people prefer to define the function inline within the function call, but I'm focused on readability.

Look at this: a basic handler function in Go uses this signature—the `ResponseWriter`, which is an interface, and the `Request`, which is a concrete type. This is a concrete type; this is an interface type.

Here's what happens: when we hit our mock server, we write 200 into the response, set the content type to `application/xml`—just like the goinggo.net web server would do—and then use the `Fprintln` function to take our raw string and stream it through the `ResponseWriter`. Brilliant.

So no matter what endpoint we hit on this mock server, this function gets called. That means we can mock any HTTP GET call we want. From our code's perspective, it looks like we went out over the network—it didn't actually leave the machine, but from the program's point of view, it did go out and perform the expected operation.

Now watch what we do with our mock server. We check that our status is okay. We call the `mockServer` function and get back our server pointer. At this point, we've stood up an actual web service. Any time we hit it, this function will execute.

We must defer the close call because we always need to clean up after ourselves. Given the need to test downloading content like we have, when we check this URL, look at the `.URL` field off of our server pointer. Whatever endpoint is provided by the mock server—that's what we're going to hit. We're not going to hit the actual goinggo.net server; we're going to hit our mock server, which runs on localhost on some random port.

And if you extend that localhost URL with any path you want, that's fine—it will still hit this same function. You can inspect the request and even add conditional logic here to simulate different endpoints or behaviors beyond just the port, allowing you to test various scenarios.

Great. We expect this status code. So now we call `GET` just like we always have, but using this localhost URL. The rest of the code proceeds as usual: checking the status, unmarshaling the response into our document, and validating things like the number of items.

Remember, this test previously took about 800 milliseconds on the network I was using. Let's see how fast it runs now that we're mocking the call and not leaving the machine.

I'm already in the correct directory. There's `example3_test.go`. I'm going to run `go test -v`. What I want you to notice is how fast this test runs.

It took 14 milliseconds—not 800, but 14. We never left the machine. You can see the endpoint we hit—it was provided by our mock server. That port number, 49897, is the randomly assigned port. When we hit that localhost URL, we executed the code inside our handler function. We successfully mocked the call and got back exactly what we expected: a valid document with a 200 status.

Look at this: a test that used to take nearly 900 milliseconds, and which required external server access, is now fully mocked. And I feel very confident that this GET call works because our mock is as close to reality as possible without actually hitting a real server.

Even better, from my perspective, is that it's not just about the happy path anymore. I can now simulate all kinds of failure scenarios—what happens if this fails, or that fails—conditions I wouldn't be able to reliably trigger on an external server. I can mock a wide range of failures and validate that our code properly handles everything that could possibly go wrong.