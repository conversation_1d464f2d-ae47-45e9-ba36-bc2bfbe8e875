Welcome to lesson three, data structures—perhaps one of the more important lessons we’ll cover early on. They’re all important, but this one is particularly crucial because it introduces mechanical sympathy and data-oriented design. We’ll explore why Go includes only arrays, slices, and maps. That was one of the things that confused me early in learning the language: Where are my lists? Where are my stacks? Where are my queues? No, no, no—<PERSON> has arrays, slices, and maps. In this lesson, we won’t just learn the mechanics and semantics of these data structures; we’ll also examine the historical reasoning behind <PERSON>’s design choices.