We're going to start learning about channels. Channels are a way of doing orchestration in our multi-threaded software. As we've discussed, there are two things you have to worry about: synchronization and orchestration. We've already seen orchestration with wait groups—essentially keeping count of tasks and waiting for goroutines to finish. Then we looked at atomic instructions and mutexes for synchronization. Remember, synchronization is about getting in line and taking turns, while orchestration is about interaction. Wait groups aren't synchronization; they're orchestration because we're waiting for multiple goroutines to report that they're done, and the underlying mechanism is just a decorated counter.

Channels allow us to move data across goroutine boundaries and perform orchestration—not by queuing up in line, but through active interaction. But before we look at any channel code, I want to set your mindset around how you should think about the behavior of channels: their semantics. This will help us understand code later, because I'm going to refer back to these concepts. Too many people view channels as a data structure—a queue, or a synchronous queue. That’s one of the first things I want you to remove from your thinking. Don’t think of channels primarily as a data structure. Instead, focus on behavior—specifically, signaling.

Always focus first on the signaling behavior and then consider how the channel enables that behavior. Mechanics help us understand how things work under the hood and assist in debugging, but it's the semantics that let us understand how a program will behave. Semantics tell us the impact our software will have on the system, while mechanics provide the implementation details.

When it comes to channels, think about one word—and one word only: signaling. Signaling is the semantic. Remember: pointers are for sharing, and channels are for signaling. The idea is that a goroutine sends a signal to another goroutine. When we talk about signaling, the very first thing we must consider is signaling guarantees—specifically, delivery guarantees. Do you need assurance that a signal sent by one goroutine has been received by another? This is the first question we should always ask when designing orchestration. Does the sending goroutine need confirmation that its signal was received?

Note that I use "send" and "receive," not "read" and "write." This is not I/O—it's signaling: sends and receives.

If you need a guarantee, use an unbuffered channel. If you don’t need a guarantee, a buffered channel may suffice. But let me explain where these guarantees come from.

Imagine this scenario: I have a colleague—let’s call him Jack. Often, I ask Jack to handle a task for me because I’m busy and he has free time. I hand him a piece of paper with instructions: “Hey, can you take care of this?” This happens regularly. I go to Jack, give him the task, and he completes it. But last week, something went wrong. I gave Jack the instructions, but the task wasn’t done. When my manager asked me if it was completed, I said yes—I had given it to Jack. But the manager replied, “It didn’t happen.” When I confronted Jack, he denied ever receiving it. He threw me under the bus.

Now, I need a guarantee: if I hand something to Jack, I must know he received it. This is critical. This is what we mean by signaling with guarantees.

So here’s how it works. One morning, I come in with a new task. I need a guarantee that Jack gets it. I go to his office, but Jack isn’t there. I can’t proceed with my day until I hand this to him, and I don’t know when he’ll arrive. So I wait—stuck in his office, with no idea how long it will take.

Eventually, Jack arrives. I hand him the paper. At this moment, I am in a sending state—I’m signaling to Jack. To get the guarantee, my send and his receive must happen simultaneously. The guarantee comes from the fact that the receive occurs just before the send—nanoseconds before—but it happens. Jack pulls the paper from my hand. I look at my hand and see it’s empty. I know he has it. There’s no way he can deny it later. I now have a guarantee that the signal was received.

Later, I need the results. Again, I can’t move forward until Jack finishes. I return to his office and ask for the results. If he’s not done, I wait. Only when he completes the work does he perform the send, and I perform the receive. Again, the receive happens before the send—Jack releases the result, I take it, and the signaling is complete.

Understand this: guarantees are essential. They ensure atomicity and consistency. Synchronization cannot be left to chance. Guarantees make our applications predictable. They are foundational. If I told you I wanted to sell you something without a guarantee, you’d hesitate. That’s how important guarantees are.

The benefit of signaling with guarantees is predictability—no randomness, no guessing. But there’s a cost. Engineering is about understanding trade-offs, and nothing is free. The cost of guaranteed signaling is unknown latency. I had to wait for Jack to arrive. I had no idea how long that would take. Then I had to wait again for him to finish. Unknown latency is the price we pay for guarantees.

Now, suppose I can’t tolerate that latency. I’m tired of waiting in Jack’s office every morning. I’m tired of waiting for him to finish before I can proceed. But Jack and I have reconciled. He promises not to throw me under the bus again, and I agree to buy him lunch every Monday for a year. I need efficiency. I can’t afford unknown latency.

So we agree: I’ll walk away from the guarantee. Instead, Jack will leave space on his desk—a buffer—where I can leave the task. I place the paper on his desk and walk away. In this case, the send happens before the receive. I never know exactly when he’ll pick it up. I hope he does, but I can’t be sure. The data is in limbo. I’m still responsible for it, in a sense.

Eventually, Jack arrives, performs the receive, and works on the task. When he’s done, he places the result in a buffered channel where I can retrieve it later. If I try to send and his desk is full—no space—I block. If I try to receive and the result isn’t there yet, I also block. So even without guarantees, blocking can still occur.

The point of abandoning the guarantee is to reduce latency. But in doing so, we also give up the benefits: predictability, lower risk. We accept more uncertainty to improve efficiency. This is about managing back pressure in our application. Just as mutexes should protect only the minimal critical section to reduce latency, we sometimes choose buffered channels to keep things moving.

There are times when the cost of unknown latency—of back pressure—is greater than the risk of no guarantee. Engineering is about understanding these costs.

Another key point: we can signal with data or without data. Signaling with data is inherently one-to-one: one goroutine sends to one receiver. Signaling without data, however, can be one-to-one or one-to-many. Multiple goroutines can receive a signal without data. But when data is involved, even if copied, it’s still a one-to-one exchange.

For signaling with data, use an unbuffered channel if you need a guarantee; use a buffered channel if you don’t. There’s also the concept of delayed guarantees. A buffer of one can keep the wheels turning while still allowing us to detect problems quickly.

Imagine I’m pulling boxes off a conveyor belt and handing them to Jack. If Jack finishes each box quickly, I can hand him the next one immediately—no latency. But if he’s slower, I end up waiting each time. To reduce latency, I can introduce a buffer of one: I place the box on the floor in front of him—only one spot available. If the spot is empty when I return, I know Jack took the previous box. I place the next one and move on.

But if the box is still there, I stop. This is crucial. We don’t want to compound problems. If the buffer is full, something has gone wrong—maybe Jack is stuck, maybe the database crashed. The goal isn’t just to optimize for smooth operation; it’s to detect failure early. We apply brakes, identify the issue, and attempt recovery.

Buffers don’t inherently improve performance. We use small buffers—not to increase throughput, but to smooth out minor delays while still exposing back pressure quickly. A buffer of one is often ideal: it reduces significant latency during normal operation and flags problems almost immediately.

Now, signaling without data is typically used for cancellation and deadlines. Here, we care about state changes. A channel has three states: nil, open, and closed.

A channel’s zero value is nil. Since it’s a reference type, we use value semantics. A nil channel blocks on any send or receive operation. Why would you want that? Imagine processing a network stream or a queue, and you need temporary throttling or rate limiting. In an event loop, you might disable certain channels by setting them to nil for a few iterations, then restore them later.

To make a channel usable, you must create it using the built-in `make` function. You can’t use a literal because you can’t pre-populate a channel with data at construction time. Once created, the channel is in the open state, and sends and receives are allowed—provided, in the case of unbuffered channels, that both operations occur simultaneously. For buffered channels, sends succeed if there’s space; receives succeed if there’s data.

Finally, a channel can be closed using the built-in `close` function. I dislike that it’s called "close" because of the baggage—it’s not about memory cleanup. Closing a channel is a state change: from open to closed. This is how we signal without data.

Think of closing a channel like turning off the lights in a room. If there are a thousand people, they all see the state change instantly. But once the lights are off, you can’t turn them back on with the same switch. Similarly, a closed channel cannot be reopened.

If you try to send on a closed channel, your program panics—it’s a violation of integrity. But you can always receive from a closed channel. Receiving from a closed channel returns the zero value and indicates the channel is closed, which is how we implement signaling without data.

As we move forward, everything I’ve discussed revolves around signaling semantics—not the internal mechanics of channels. I won’t teach the internals because understanding them can actually hurt more than help. In concurrent programming, we must focus on behavior and semantics.

To summarize: a channel supports two types of signaling—**with data** and **without data**. Signaling with data is one-to-one. Signaling without data can be one-to-one or one-to-many.

Understand your guarantees: does the sender need confirmation that the signal was received? If yes, use an unbuffered channel and accept unknown latency for the sake of predictability. If no, use a buffered channel to reduce latency—but accept higher risk.

Buffers don’t provide performance gains. Avoid large buffers. Use small ones—just large enough to keep processing flowing during brief delays, but small enough to expose back pressure quickly so you can respond to upstream issues.

Remember the three states: nil, open, and closed. Use them appropriately.

Keep all these signaling semantics in mind as we begin to read and write channel-based code.