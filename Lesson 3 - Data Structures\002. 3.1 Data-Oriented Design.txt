Data-oriented design. Before we get into data structures in Go, I really want to share some information about data-oriented design. It's a really important aspect, I believe, of the language, and one of those things you're going to have to start shifting your thinking toward, moving away from object-oriented design.

I've said a few times already in this training material that data-oriented design is about understanding that every problem you solve is a data problem. We are all data scientists at the end of the day. Integrity comes from the data, our performance comes from the data, everything we do—our mental models, our systems—everything stems from the data. If you don't understand the data you're working with, you don't understand the problem you're trying to solve, because all problems are specific and unique to the data you're dealing with. Data transformations are at the heart of everything we do—every function, every method, every encapsulation is ultimately about the data transformations we're performing.

But here's a key point: everything we've been discussing so far happens in the concrete. Our problems are solved in concrete data, our manipulations, our memory mutations—everything occurs at the concrete level. And here's the challenge: when the concrete data changes, your problems change. And if your problems change, then your algorithms must change too. There's nothing inherently wrong with that, except that when data changes and algorithms change, it can trigger cascading changes across an entire codebase, which creates a lot of pain.

This is where we start focusing on how to decouple our code from these data changes so that cascading changes are minimized. We'll be talking about this in detail later in the course when we get into methods, interfaces, and related concepts. But here's the thing: if you're abstracting in a general way, building abstractions on top of abstractions, you're moving away from everything we've discussed so far—especially the goals of optimizing for correctness and readability. You're distancing yourself from the ability to truly understand the mental models of your code because it becomes too abstract.

What we need is a balance: decoupling, yes, but with thin layers of decoupling to manage change. Change will always be important. But if you're starting to write code and you're uncertain about the data, that doesn't give you permission to guess. It's a directive to stop—stop what you're doing, sit back, and ask yourself: what are the data transformations in front of me? You don't need to know all of them upfront, but you should only code the ones you're confident about, the ones where you clearly understand the input and the output.

If you're solving problems you don't actually have, you're creating more problems than you're solving. We write code for today and design architecture for tomorrow, but don't add more code than you need today. We've already discussed how extra code leads to more bugs, more lines where bugs can hide. I keep reiterating this because data-oriented design is about understanding your data, writing only the code and algorithms you need, and eventually decoupling those concrete algorithms to handle future data changes.

Everything we do must focus on minimizing, simplifying, and reducing the amount of code required to solve each problem. We're going to learn very soon about mechanical sympathy. If performance matters, then you must care about how hardware and the operating system work. And again, this all ties back to data-oriented design. Data, data, data, data, data.

You're going to hear me talk a lot—constantly—about data-driven everything. Data drives everything. Everything is moving in that direction. We're going to start looking at data structures and understanding why Go provides only arrays, slices, and maps. I want to show you the mechanical sympathies these data structures have, how they don't hide the cost of operations, and how we can achieve greater efficiencies when working with data. We'll be able to write algorithms that are both readable and highly performant.

Because again, on the scale of where you lose performance in Go, algorithmic inefficiency is actually near the bottom. The real performance costs come from networking and I/O latency, allocation and memory latency, and the inability to write code that efficiently manages and works with data. These are the things we're going to focus on now.

I'm going to start breaking you down and building you back up—moving you away from object-oriented design principles and guiding you toward data-oriented design principles.