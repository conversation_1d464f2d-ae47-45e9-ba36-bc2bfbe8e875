So now I want to show you how to do macro-level optimizations, where we're looking for low-hanging fruit and identifying potential issues in an application we've already written. I have a project ready here—a web application with a browser-based front end. Let me show it to you first. I'm in the project folder, I run `go build`, and then execute the binary. It starts listening on localhost:5000. I open a browser tab and navigate to 5000/search—the endpoint for this web application. You can see I'm ready to compete with Google by searching for anything.

What this application does is download some RSS feeds, stores them in memory for about five minutes, and allows us to search against that cached data. Every five minutes, it re-caches the feeds. To test functionality, I usually search for what my friend is doing, since he tends to be all over the news. Sure enough, he appears in CNN reports, BBC reports, and New York Times articles. He's everywhere. This gives us confidence that the search is working properly and that we have reliable results.

The system is ready for production, but before deploying, I want to get a general sense of its health—specifically around allocations and CPU performance. To do this, I need to run the server under load and eventually take some profiling data. Even without full profiling right now, I want visibility into the system's health from both scheduling and memory perspectives.

Let's start with the scheduler. I want to observe how the server on my machine handles basic load, ensuring we're not leaking goroutines and that tasks are being processed within a reasonable timeframe. I can achieve this using the `GODEBUG` environment variable. Specifically, I set `GODEBUG=schedtrace=1000`, which generates a scheduler trace every 1,000 milliseconds (one second).

I set this variable before running the project. This can be done directly in the shell or via export. It's important to redirect standard output to `/dev/null`, because `GODEBUG` writes its traces to standard error, and I don't want standard output interfering with the trace data. So I start the project again and verify it's working by going back to the browser, hitting search, and confirming the results come back correctly.

Now, every second, I receive scheduler statistics. Note that this trace only shows goroutines that are either executing or in a runnable state—those that are waiting do not appear. Let's walk through the output quickly.

Each line shows the timestamp when the trace was taken, the number of OS threads (procs) in use, and how many of them are idle. Initially, all are idle because no load has been applied yet. We also see the total number of OS threads, spinning threads (those without work that are actively looking to steal work or avoid context switching), the global run queue length, and the local run queue lengths per P (per logical processor). I have eight Ps here.

Again, only goroutines that are running or runnable are visible. This trace runs every second. Now I want to apply load. I've set up the `hey` tool to make POST requests with 100,000 concurrent connections. I'll run 100,000 requests targeting the search endpoint for "trump".

When I start the load test, the scheduler stats begin to change. If I pause briefly, I can observe that all procs are busy—none are idle, and none are spinning. There were 59 goroutines in the global run queue waiting to be picked up. You can also see the distribution across local run queues. The scheduler is trying to balance the workload as evenly as possible, though some variation occurs due to differing task durations.

As the load continues, I notice a few positive signs: a large volume of work comes in, builds up temporarily, and then gets processed efficiently. At one point, a burst of work arrives, but it's cleared quickly. There's no indication of goroutine leaks—though strictly speaking, a goroutine leak typically involves blocked or waiting goroutines, which wouldn't show up in this trace. However, if leaking goroutines cycled between runnable and waiting states, we'd expect to see run queue lengths grow over time. Instead, once the load completes, the number of runnable goroutines drops back to zero. This demonstrates strong system health—no leaks, efficient processing, and proper cleanup.

This gives me confidence in the scheduler's behavior. Let's stop the load. The workload is complete, so now I want to examine CPU and memory usage. I'll reduce the load to 10,000 requests and switch from `schedtrace` to `gctrace`. Setting `GODEBUG=gctrace=1` enables garbage collection tracing, outputting a line each time a GC cycle starts.

Right now, there's no output because no GC has occurred yet. Let's trigger one by performing a single search from the browser. After completing that search, we see two garbage collections occurred. Let's break down what these numbers mean to assess system health.

Each GC line starts with `gc 1`, `gc 2`, etc., indicating the sequence number of the GC cycle. Next is the elapsed time in seconds since the program started when the GC began. Then we see the CPU utilization percentage—here it's zero, meaning the GC completed very quickly. 

The wall clock time is broken into three parts: stop-the-world time before concurrent marking (to bring all Ps to a safe point), concurrent marking time, and final stop-the-world time for cleanup. The values on the left and right of the plus signs represent stop-the-world durations. These should ideally not exceed 100 microseconds combined. In our case, we see 78 microseconds and 98 microseconds—close, but acceptable. The middle value is concurrent time, which should constitute most of the GC duration.

The CPU clock breakdown mirrors this but splits the concurrent phase into three sub-phases. Documentation in the runtime explains these in detail, but for our purposes, we focus on the overall concurrent time and stop-the-world latency.

Next is heap information—the most critical section for detecting memory issues. This includes the heap size before GC, after GC, and the live heap size (the amount of reachable data retained). In this case, we started with a 4 MB heap and ended with nearly nothing in the live heap, indicating most allocations were transient and properly collected. The goal for the next GC cycle is also shown, along with the number of logical processors.

You may have noticed a forced GC. When the program is idle enough, the runtime forces a GC to reduce heap size. Here, it reduced the heap from 4 MB to 3 MB. Heap sizes are reported in megabytes.

Additionally, you see output from the scavenger—a background process that returns unused memory to the operating system. In this case, no memory was released because the heap is small, but the scavenger is active and ready to reclaim memory when beneficial.

Now, let's run a quick load of 10,000 requests and observe how the heap behaves under sustained pressure. As the load runs, frequent garbage collections occur—don't panic. A busy Go program often has GC cycles every millisecond or two. Our goal is to maintain the smallest possible heap while keeping GC latency under 100 microseconds.

Looking at the trace, we performed approximately 2,192 garbage collections during this load. Checking the interval between GC events, we see pacing around one to two milliseconds—acceptable if stop-the-world times remain low. However, many stop-the-world durations are approaching or exceeding 100 microseconds—31, 70, even higher in some cases. That's concerning.

We also observe significant heap growth—from 5 MB to 10 MB, doubling in size. This suggests we're creating many short-lived goroutines or allocating large amounts of transient data in bursts. Combined with erratic heap fluctuations, this pattern indicates potential over-allocation.

Frankly, this behavior worries me. It suggests we might be allocating memory inefficiently—perhaps creating unnecessary objects or failing to reuse buffers. While the GC is keeping up, the high frequency and latency of stop-the-world pauses could impact responsiveness in production.

The heap profile tells me we need to investigate further. The next step is to capture a memory profile—and eventually a CPU profile—from the application to identify specific allocation hotspots and optimize performance.