We're about to enter the section on Go tooling that focuses on tracing and profiling. This is exciting material, and we'll likely have some longer videos here because I'll be doing live coding and exploring the tooling in detail. You'll be able to stop, pause, and examine what I'm doing at your own pace.

I'm currently running on Go 1.11 beta one, and there are some new features in this beta related to tracing that I haven't had the chance to cover yet. I encourage you to revisit this training material over time, as we'll be updating it. Plus, feel free to reach out—I'm at every conference, actively writing and sharing knowledge. We'll keep this content current as Go evolves.

I want to share a quote from <PERSON><PERSON> that resonates deeply: "Those who can make you believe absurdities can make you commit atrocities." This ties back to the importance of validating your results when profiling and tracing. Don't be misled by misleading data and make poor engineering decisions as a result. Now, our focus is shifting—not necessarily toward correctness, but toward performance. The idea is that we have a piece of software that isn't running fast enough, and we want to understand why so we can make targeted improvements.

There are two main concepts here: profiling and tracing. I'll focus on two types of profiling: CPU and memory. I won't be covering blocking profiling or mutex profiling, as I don't find them particularly valuable for most use cases. Profiling isolated aspects like blocking behavior in small parts of a program is difficult to interpret and act upon. I've largely stopped teaching these topics. Tracing, however, will help us identify what *isn't* happening in our programs—I'll demonstrate this later.

Here's how profiling works: we hook into the operating system, which periodically interrupts your program at regular intervals, halts all threads, collects program counters, and then resumes execution. Because of this, your program will run slightly slower during profiling—but that's acceptable. We're looking for hot paths. From a CPU perspective, we want to identify which functions consume the most time. On the memory side, we're focused on two key aspects: first, the number of values being allocated on the heap—these are garbage, especially if they're short-lived or transient—and second, overall heap size. We want to understand how much memory we're using and whether we can reduce it.

This becomes critical when performance matters—especially in systems like databases, where both memory footprint and allocation rates directly impact efficiency. High allocation rates increase garbage collection (GC) pressure, which can slow down your application even though GC cycles are typically around 100 microseconds. The less work the GC has to do, and the slower the allocation rate, the better.

When seeking performance improvements at a macro level, allocations should be your first focus. At the micro level, CPU performance usually takes precedence. This is based on my own experience and workflow. We'll explore both macro and micro-level optimizations before we're done.

A few best practices: the machine must be idle during profiling—repeat: it must be idle, it must be idle. If you're profiling on shared hardware, understand that your results are relative to that environment. I'm doing all of this on my Mac, which is a noisy machine. Your results may vary significantly on different hardware or in production environments. Avoid browsing the web, watching cat videos, or running other applications while profiling. Power-saving modes and thermal throttling can introduce fluctuations. For reliable historical benchmarks, consider using dedicated, bare-metal hardware. And if you care about consistency over time, avoid updating the operating system during your testing period.

We'll work relative to my Mac setup, but aim for results that correlate with production systems. While there are many types of profiling—CPU, memory, blocking, mutex—I'll focus on CPU and memory profiling because they offer the best return on investment. However, the techniques I demonstrate can be applied to other profiling types as well.

As we discussed earlier in the context of validation, always perform one profile at a time, ensure the machine is idle, and validate your findings. As you analyze profiles, watch for specific signals: `mallocgc` calls indicate heap allocations; mutex-related calls suggest potential blocking or synchronization delays; system calls for read/write operations point to I/O or context-switching latency.

Remember the four main sources of performance pain: external latency (network, disk, device), internal latency (mutexes, synchronization), allocations (heap allocations that burden the GC), and data access patterns (how efficiently data moves into the processor). Finally, algorithmic efficiency—this is what CPU profiles will reveal. Memory profiles, in turn, expose allocation behavior.

Keep in mind that while large objects affect memory consumption and possibly GC frequency, it's the sheer volume of small, short-lived allocations that most heavily impacts GC workload. There are many small optimizations we can apply to mitigate this.

There are fundamental rules about performance that I’ve repeated for good reason: never guess. Always rely on relevant, accurate measurements. Only profile when you’ve determined something is genuinely performance-critical. If it’s already fast enough, don’t waste time optimizing. Ensure your measurements are precise and repeatable.

I’ll be using a tool called *hey*, developed by JBD—Yana, as I like to call her—who has done incredible work on the Go team at Google. She now contributes broadly to observability across communities. If you’re not following Yana JBD, you should be—she shares invaluable insights. She created *hey*, a command-line tool we’ll use to generate load on our applications.

I also want to highlight *ngrok*—ngrok.com. If you’re not using ngrok for load testing or traffic capture, you’re missing out. It’s an exceptional tool for simulating and measuring real-world traffic. I can’t recommend it highly enough. That said, for now we’ll stick with *hey*—it’s simpler on the command line and sufficient for our needs.

If you’re unfamiliar with Dave Cheney, look him up. He’s made tremendous contributions in this space, and I’ve learned a great deal from him. Study his talks and blog posts. If you ever get the chance to attend one of his workshops—something I’ve never managed because I’m usually running my own—take it. You’ll learn an immense amount. Dave works in the trenches daily, is a Go contributor, and has deep knowledge of the runtime internals.

I’ve included links to several videos and blog posts in the training materials that I’ve found helpful—these will support your learning as we go.

We’ll also use a few operating system tools. The `time` command works on Linux and macOS (though not natively on Windows) and allows us to measure program execution time without building timing logic into our code. Since Windows 7, we’ve had access to `perf`—a Linux-only tool. Thanks to frame pointers now being included in Go binaries, `perf` works effectively with Go programs. It provides a low-level view of execution: context switches, page faults, instruction counts, branch misses—mechanical sympathy metrics we’ve discussed throughout this course. While `perf` is Linux-only, Go programs fully support it.

These are the tools and concepts we’ll be working with. I’ll show you how to interpret stack traces—powerful diagnostic artifacts—and how to generate core dumps (though hopefully you’ll never need them). I’ll demonstrate micro-level optimizations using benchmarks, and macro-level optimizations by analyzing real-world programs for high-impact improvements. Finally, I’ll walk through how the tracing tool works, including new capabilities in Go 1.11 that help you see not only what *is* happening in your software, but what *isn’t*.

Go continues to improve these tools with each release, making them more powerful and easier to use. This rich tooling ecosystem is one of the key reasons developers become deeply committed to the language. I’ll do my best in this section to teach you everything I can and show you how to use these tools effectively.