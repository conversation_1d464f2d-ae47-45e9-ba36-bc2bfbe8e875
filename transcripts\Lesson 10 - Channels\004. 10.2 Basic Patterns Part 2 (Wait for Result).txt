Our next basic pattern is "wait for result." It is the opposite of "wait for task." We're going to use this pattern in scenarios like drop patterns and fan-out patterns. Let's walk through it again using the familiar analogy of a manager and an employee. We're going to hire an employee, starting on line 53. Again, we see an unbuffered channel. As before, this represents signaling—this time with guarantees and with data. We want guarantees, and we're going to signal with string data.

We move to the launch. Here we are—the manager—starting our path of execution on our own core. Then we spin off and start the employee's path of execution. That's what you see on line 56: a literal function call with the keyword `go`. This time, however, the employee already knows what they're supposed to do. They don't have to wait for instructions from us. This allows us to reduce some latency because the employee is immediately aware of the task at hand. The employee begins working right away.

The question is, how long does it take to complete this work? The reality is that the time required is unknown. While the employee is working, we continue executing and reach line 62. At line 62, we are blocked—that's the channel receive. We're blocked on a unary channel operation. What we're really doing is waiting for the employee to finish so we can proceed. We are now blocked here, specifically on the receive operation.

The question again: how long will we remain blocked? That is also unknown. We don't know how long the work will take. Eventually, the work gets done. Once completed, on line 58, the employee signals with data—the result—sending it back to us, perhaps like handing over a piece of paper. At this point, we are already in the channel receive operation, blocked and waiting for this data. Since I'm on the receiving side, I'm inside that unary operation, waiting.

Now, the send operation occurs. The send comes in from line 58. We were already waiting in the receive. The receive was in place first. There it is—the send arrives. The receive happened first on line 62, the send happens on line 58. Because the receive was waiting, we have a guarantee that the send will complete. We get to move first—nanoseconds before the send finishes—and we obtain our data. Then the employee gets to proceed. The employee knows—this is the second step—that we have successfully received the result. There is a guarantee; there's no way we can leave the employee hanging or "throw them under the bus," because the design ensures the send only happens when there's a ready receiver.

We come in blocked on the receive. An unknown amount of time passes while the work is being done—we don't know how long we'll wait. Finally, the work completes. The send and receive synchronize. The receive occurs first, the send follows—perhaps only by nanoseconds, but the order is guaranteed. We can't lose the sequence of operations, especially when observing print statements or execution order. Then, both sides are able to move forward.

This is "wait for result." We'll use this pattern again in drop patterns and fan-out patterns. It's another powerful foundational pattern. These two—wait for task and wait for result—are two of the core patterns we've just examined. The next core pattern will involve signaling without data, which we'll use primarily in the context of cancellation and deadlines. We'll call that pattern "wait for finished."