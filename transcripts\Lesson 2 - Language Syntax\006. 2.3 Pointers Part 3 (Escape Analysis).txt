Alright, so we just looked at the base mechanics for value and pointer semantics. These are going to be very important going forward. Again, I want to make sure that we can read code in Go and understand the cost and the impact that code is having. Memory is going to be a big part of that.

Now, there's a significant aspect of these semantics—value semantics and pointer semantics—and it's the cognitive load around understanding how to manage memory. One of the most beautiful things about Go is that it takes that burden off of us. We don't necessarily have to worry about making mistakes with where things are located or how we access them, because such mistakes can cause very serious problems. So our value and pointer semantics are going to be crucial, and we need to be able to read and understand how things behave. Let's talk about this again.

Value semantics have the benefit of allowing mutation and isolation within our own sandboxes, but they come with the cost of inefficiency. We end up making copies of data as we cross program boundaries. Pointer semantics, however, solve the efficiency issue. We have one piece of data that we share; everyone can mutate it, and everyone sees those changes. But this comes with the cost of side effects and much more work for us to ensure we're not corrupting data or having values changed behind the scenes without other parts of the code or goroutines being aware. This is complex stuff—I'm not going to hide that from you.

But if we balance value and pointer semantics properly, leveraging the language features that help reduce the cognitive load of memory management, we'll be in a much better position. I want to show you another example of these semantics and how the language really helps reduce our cognitive load. This will also be important for understanding the impact and cost our code has on the machine.

I have a user-defined type here with fields for username and email. I've defined two functions: one called `createUserV1` and one called `createUserV2`. As I mentioned before, Go doesn't have constructors—and that's a good thing. Constructors can hide cost. What we do have are what I call factory functions. A factory function is a function that creates a value, initializes it for use, and returns it to the caller. This is great for readability—it doesn't hide cost, we can see what's happening, and it lends to simplicity in construction.

I don't like calling these constructors because that brings unnecessary baggage. I call them factory functions. In this code, I have two versions of the createUser factory function. Let's start with version one.

The first thing I want you to notice is the return type of the function: it returns a value of type `user`. This immediately tells us that this factory function is using value semantics. Throughout this course, I'll keep asking you what semantics are in play—this is critical. With version one, value semantics are in play because the caller will get their own copy of the value after the function returns.

If we were to visualize how this function behaves, we'd probably imagine the following: we're in `main`, and we call version one. Inside this function, on lines 25 through 28, we perform literal construction—we're constructing a value of type `user` using a struct literal. We end up with a variable named `u` containing at least the name "Bill". At this point, construction happens within the current stack frame of the active goroutine. This is the active frame—we're operating here.

Now look at line 30. The ampersand operator is very powerful from a readability standpoint. The ampersand means sharing. When I read line 30, I see that we are sharing the user value down the call stack to the print function. At that point, a new frame is created for `print`, and we're sharing that value down. When `print` wants to display the value, it does so through indirection via a pointer. But this is safe because, while `print` is the active frame, all memory associated with frames above it is still valid. Sharing a pointer down the call stack in this way is perfectly safe.

Eventually, `print` returns, and we're back in the original frame. The goroutine resumes operating here. Then, on line 32, we return `u`. This is a value semantic return—we make a copy of `u` and pass it back up the call stack. Now, when the goroutine in `main` is active again, it operates on its own copy of `u`, which contains "Bill". This is fine because we no longer need the memory used in the lower frame. This is classic value semantics. You can see the integrity we gain: the goroutine operates on its own copy, which becomes the point of truth. We don't care about what happens in the deallocated frame below. That memory will be overwritten on the next function call, but it doesn't matter—our semantics are strong and clear.

Now let's look at version two of this code. Notice the return type: it's no longer a value. Version two uses pointer semantics. It returns a copy of the address of the value being constructed. This is very common in Go.

Let's draw this out. I'll clean up the stack. We have `main`, and we call version two. The code looks almost identical to version one, with just a few subtle differences. On lines 39 through 42, we appear to be doing the same construction: creating a `user` value initialized to "Bill". We share it down the call stack again. The active frame is here, the goroutine is operating here, and we share the value down.

But this time, on the return, we're not copying the value—we're copying the address of the value. Remember, everything in Go is pass-by-value; the data being passed is either a value or an address. On the surface, it might look like this is what happens: we return the address up the call stack, and once we're back in `main`, that pointer points into a stack frame that's no longer valid.

If that were true, we'd be in serious trouble. Think about it: once `main` makes another function call, that stack frame gets reused and overwritten. The pointer would then point to corrupted data. We'd have a major integrity issue and potential side effects. This would be extremely dangerous and complex to manage. If we didn't have the compiler handling this, it would be our responsibility as developers—and that's why memory management has been so error-prone in other languages.

But this is not what happens. The Go compiler is extremely powerful. It performs static code analysis, and in this case, it uses a technique called escape analysis. Escape analysis determines whether a value can be allocated on the stack—or whether it must "escape" to the heap.

Our first priority is always to keep values on the stack. Stack memory is already available, very fast to use, and self-cleaning. The garbage collector doesn't get involved with stack memory at all—only with heap allocations. In Go, an allocation occurs when escape analysis determines that a value cannot be safely kept on the stack and must instead be placed on the heap. That's what happens here: to avoid an integrity issue, the value must escape to the heap.

You might wonder: why doesn't the garbage collector need to manage the stack? How is the stack self-cleaning? Recall the concept of zero values. Every time we make a function call, we take a new stack frame and zero it out. Every byte and bit is set to its zero value—nothing is left random. When we make another call, we take another frame and clean it. The stack cleans itself on the way down. On the way back up, we don't clean the memory because it would be inefficient—we might need it again. So memory is left intact on the way up and cleaned on the way down. This self-cleaning behavior means the garbage collector doesn't need to touch stack memory, giving us tremendous performance benefits.

We want to leverage value semantics and keep values on the stack whenever possible—not just for performance, but also for the benefits of isolation, reduced side effects, and simpler reasoning about code. But when values escape to the heap, the garbage collector must manage them.

So what actually happens in version two? This is one of the most beautiful aspects of Go: the syntax abstracts away the machine. It's powerful and elegant. You don't always need to care where data is stored. But when performance or debugging matters, understanding escape analysis and memory layout becomes essential.

Let's walk through version two with escape analysis in mind. We have the stack: `main` calls `createUserV2`. During construction on line 39, escape analysis kicks in. It doesn't care about how the value is constructed—construction in Go tells you nothing. What matters is how the value is shared. Sharing tells us everything.

Because line 46 returns `&u`—sharing the address up the call stack—escape analysis knows this value cannot stay on the stack. It must be allocated on the heap. So, even though we're using value semantics in construction, the compiler moves the allocation to the heap.

Here's what's fascinating: `u` is still a value of type `user` with the name "Bill", but it's not on the stack—it's on the heap. And since the active goroutine's frame can't directly access heap memory, the compiler automatically uses a pointer behind the scenes to access it. From a syntax perspective, we still manipulate `u` as a value. The language lets us keep our code simple, using value semantics, while the compiler handles the complexity of heap access via pointers.

When we share `u` down the call stack to `print`, we're passing a copy of the heap address. On the return, we pass a copy of that same heap address back to `main`. There's no integrity issue: even though the original stack frame is gone, the heap-allocated value remains valid, and `main` holds a pointer to it.

But there is a cost: an allocation occurred. The value is now on the heap, and the garbage collector must manage it.

Here's a guideline I want to emphasize: the ampersand is a powerful readability tool. Never walk away from it. Consider this: if all you saw was `return &u`, what would you know? Based on what we've discussed, you'd know there's a cost—this line likely causes an allocation because the value is escaping to the heap. You can read that cost directly from the syntax: `&u` means sharing up the call stack, which triggers escape analysis and heap allocation.

But what if I removed the ampersand? What if it just said `return u`? Then the line tells you nothing about cost. You'd have to read the entire function to understand what's happening. You've lost readability.

This becomes even worse with clever code like this: during construction, instead of `u := user{...}`, you write `u := &user{...}`. Now you're using pointer semantics at construction time—even though you're assigning to a variable. This is a nightmare. It makes code harder to read, mixes semantics, and obscures intent. Anytime you mix value and pointer semantics, you create problems.

So here's the rule: never use pointer semantics during construction when assigning to a variable. Always use value semantics. This gives you the highest level of readability. You can clearly see sharing down and up the call stack. You can perform your own mental escape analysis.

The only time you should use pointer semantics in construction is when you're returning it directly or passing it directly to a function call—without assigning it to a variable. That's when the syntax makes sense.

If you start a variable's life as a pointer, you lose readability and violate the principle that construction tells you nothing—only sharing does. This is what we should avoid.

And here's some truly nasty code I've seen: using pointer semantics in construction (`&user{}`), then returning a value (`return *u`). You're using pointer semantics to create, then value semantics to return. And on line 46, you have to dereference to get back to a value. What's ironic is that in such cases, the value often never escapes the stack—it could have stayed on the stack the whole time. Yet the code uses pointers unnecessarily. This kind of semantic mixing is painful to see. It defeats the purpose of Go's clarity.

We must maintain semantic consistency. Don't construct values to variables using pointer semantics. Use the ampersand in the right places—where sharing is explicit.

One more powerful tool: when you use `go build`, you can pass GC flags to get an escape analysis report. We'll use this during profiling. Instead of producing a binary, the compiler outputs a report showing which values escape to the heap and why. Profilers can tell you what is allocating, but not why. This report tells you why. It shows, for example, that `u` escapes to the heap because of the return statement—sharing up the call stack forces the escape.

Don't use this while coding—it's a profiling tool. But when you need to understand allocations, it's invaluable. The report will clearly state that `u` escapes to the heap due to the return, confirming that escape analysis is doing its job to preserve memory safety. We now have an allocation, and the garbage collector is involved—but at least we understand why.