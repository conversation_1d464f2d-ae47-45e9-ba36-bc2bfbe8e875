We just reviewed the mechanical sympathies of using arrays, slices, and maps, and demonstrated how these mechanical sympathies are real in relation to hardware-level caching. I want to summarize much of what we've discussed so far regarding arrays, while revisiting earlier conversations about value and pointer semantics. This section covers important concepts that will provide a strong foundational base as we move forward into data design and data structures.

Let's start with this piece of code. On line 14, we're declaring an array of five strings—again, five being a constant of kind int. The compiler knows the size of an array at compile time because the size must be known then; you can't use a variable for an array size. We'll see that slices allow this flexibility in the next section. When I look at line 14, I see 40 bytes of memory. Remember, a string is a two-word data structure. On the Go playground, each word is 8 bytes, so each string is 16 bytes. I visualize this as five strings laid out contiguously in memory, starting at index zero through four, all initialized to their zero value—empty strings. This is the state of our `fruits` array after line 14.

Now focus on line 15. Here we have a string literal, "apple". This is a constant string that points to a backing array of five bytes: 'a', 'p', 'p', 'l', 'e', along with a length of 5. This data resides in the program's data segment. Imagine we have this string value with its pointer to the backing array. One question I often ask in class is: what is the cost—engineering cost and benefit—of the assignment on line 15? Remember, assignment is a copy operation. So what exactly is being copied?

A string is composed of two parts: a two-word data structure containing a pointer to the backing array and a length. The cost of this assignment is just copying those two words—16 bytes on a 64-bit system. This reveals something important: the string value itself is small and efficient to copy. But notice the pointer inside it. That pointer enables sharing of the backing array across multiple string values. This is a key insight: pointers are for sharing. And sharing provides efficiency—we only need one backing array even when multiple string values reference it.

Go uses only one looping construct: the `for` statement. You can use it traditionally, as shown on line 31, or with just a condition. But the `for range` loop is particularly powerful because it supports two distinct semantics: value semantics and pointer semantics.

Recall that value semantics means each piece of code operates on its own copy of the data. As we cross program boundaries—like function calls—we work with copies. The benefits include isolation and immutability: mutations don't affect other parts of the program, avoiding side effects. But we also have pointer semantics, which involve sharing. Sharing improves efficiency by avoiding unnecessary copies, but it introduces complexity: mutations can have side effects across the program, so we must manage them carefully through synchronization or orchestration.

On line 22, we see the value semantics form of the `for range` loop. We're ranging over the `fruits` array of strings. On each iteration, we get the index—0, 1, 2, 3, 4—and a local variable declared with the short declaration operator. This `fruit` variable is a copy of each element in the array. Since each element is a string, `fruit` is also a two-word structure—a copy of the string header.

Now we have three string values in play: the original in the array, the copy in the loop variable, and the backing array in the data segment. But it goes further. On line 23, we call `print`, passing `fruit` as an argument. What are we actually passing? A copy of the `fruit` variable. That means in the stack frame of the `print` function, we now have a fourth string value—another copy of the two-word header—still pointing to the same backing array.

Think about this: we have four string values—each a 16-byte header—scattered across different stack frames and the data segment, all efficiently sharing the same backing array. The only thing that might need to be heap-allocated is the backing array itself—if it were dynamically created. But in this case, it's a string literal, so it's in the data segment. Even if it were dynamic, only that backing array would require heap allocation. All the string headers can live on the stack.

This is crucial: our job as developers is to identify and balance when to use value semantics and when to use pointer semantics, aiming to minimize allocations. The string type is designed for value semantics—it's meant to be copied. Because it's small and copying it is cheap, we can keep these copies on the stack. Only the backing data may need the heap. This means the garbage collector only needs to track one heap-allocated object, not five. It also means that object may have a longer lifetime, but the stack-based headers are cheap and fast.

Your understanding of value and pointer semantics is critical—not just for performance, but for correctness. These concepts are foundational to writing efficient, predictable, and maintainable Go code. We must continue discussing them, appreciating the balance between them, and knowing when to apply each.

These semantics are not something I've invented—they are deeply embedded in the language. They are real, and we can prove it by predicting program behavior. Remember: mechanics describe how things work under the hood; semantics describe how they behave.

Let me demonstrate with this code. I'll add a print statement to separate outputs. On line 12, we declare an array of five strings called `friends`. Let's map this out. The array contains five string values: "Annie" (I'll abbreviate as A), "Betty" (B), "Charlie" (C), and two more, with lengths 5, 5, and 6 respectively.

On line 13, we display the string at index 1, which is "Betty". So we expect to see "Betty" in the output.

Now enter the loop. For the first time, we're seeing what I call the pointer semantics form of the `for range` loop. Here, we range over the indices only—we're not copying the values. On the first iteration, index 0, we do nothing. On index 1, we change `friends[1]` from "Betty" to "Jack". So now the array shows "Jack" at index 1. Then on line 18, we print the value at index 1 again. We expect to see "Jack".

If we run this, the output is "Betty" followed by "Jack"—exactly as expected.

Now let's take the same algorithm but use the value semantics form of the `for range` loop. We start over: `friends` is initialized the same way—Annie, Betty, Charlie, etc. We print index 1: still "Betty".

But now, in the loop, we range over the array using value semantics and request only the index. However, in this form, the `for range` loop makes a complete copy of the array before iterating. It does not range over the original `friends` array—it ranges over a copy.

So when we update `friends[1] = "Jack"`, we're modifying the original array. But the loop is iterating over its own copy, which still has "Betty" at index 1. When we print `friends[1]` on the second iteration, we see "Jack" because the original was updated. But when we print the loop variable `v`, which came from the copy, we still see "Betty".

The output is "Betty" and then "Betty" again. Why? Because value semantics mean we operate on our own copy. The `for range` loop, in this form, copies the entire array. So even though we modified the original, the loop is reading from the unmodified copy.

This is the essence of value semantics: behavior is based on copies. If you don't understand the semantics—how the code behaves—you cannot predict its outcome or its impact.

It's essential to choose the right form of `for range` based on your data and intent. We'll learn that the data should drive the semantics: once you decide whether your data should be handled with value or pointer semantics, your entire codebase should follow that consistently.

Now, here's an example I never want to see in real code. We have the same array of five strings. We print "Betty" as before. But now, we use the value semantics form of `for range`—yet instead of ranging over the array directly, we range over the address of the array.

Wait—no. Let me correct that. What's happening here is even worse. We're ranging over the array using value semantics, but we're taking the address of each element. That means we're iterating with value semantics (copying the array), but then using pointers to modify the original through the addresses. This mixes semantics: the iteration is based on a copy, but the mutation uses pointers to the original.

Actually, let me clarify: if we range over `&friends`, we're ranging over a pointer to the array. But in this case, the speaker is referring to a scenario where we range over the array but take the address of each element inside the loop. That creates a pointer to the original array's elements, even though the loop variable is a copy.

But the real problem the speaker is highlighting is semantic mixing: using value semantics in one part and pointer semantics in another without clarity. For example, if we range over a slice (which is a pointer-like type) but treat it as value, or vice versa.

The point is: mixing semantics makes code confusing, hard to read, and impossible to predict. We must avoid it.

We decide the semantic based on the data, then apply it consistently throughout. Not the other way around.

For those newer to arrays: remember, an array provides a contiguous block of memory. Look at this code: we have the same array of five friends. We range over it using value semantics, but this time we print the address of each element.

The output shows addresses like 150, 158, 160, 168. Two things stand out: first, the elements are adjacent—this confirms the array is contiguous in memory. Second, the stride between addresses is 8 bytes. Why? Because on the Go playground, a string is 16 bytes? Wait—no.

Wait: earlier we said a string is two words. On a 64-bit system, a word is 8 bytes, so a string header is 16 bytes. But in the address example, the speaker says the addresses are 150, 158, 160, 168—that's an 8-byte step.

Ah—this suggests each array element is 8 bytes. But that contradicts the string being 16 bytes. There's an inconsistency.

Wait—perhaps the speaker misspoke. Or perhaps in this context, the array is of type `[5]*string` or something else. But based on the transcript, he says "array of five strings" and then sees 8-byte strides.

Actually, let's reevaluate: if the addresses are 150, 158, 160, 168, that's +8, +8, +8—so 8-byte stride. But a string is 16 bytes. That doesn't add up.

Unless—perhaps the speaker meant the addresses are 150, 158, 160, 168 in hexadecimal? But that would be unusual.

Alternatively, perhaps he's referring to a different data type. But the transcript says "array of five strings".

Given the context, it's more likely a speech error. He probably meant the stride is 16 bytes. But he says "eight bytes" and "two words, every word is four"—that suggests a 32-bit system.

Ah—there it is: "two words, every word is four, four plus four is eight". So he's assuming a 32-bit system where a word is 4 bytes. So a string (two words) is 8 bytes. Therefore, the array elements are 8 bytes each, and the stride is 8 bytes: 150, 158, 160, 168 (assuming base 10, though that's odd—probably meant as examples).

So on a 32-bit system, a string header is 8 bytes (4 for pointer, 4 for length), so the array of 5 strings is 40 bytes, with 8-byte stride.

The key point stands: the elements are contiguous, and the stride is predictable. This alignment—8-byte values on 8-byte boundaries—is optimal for hardware. The CPU's prefetchers can anticipate memory access patterns, loading cache lines efficiently. This improves performance not by how fast we move data, but by how efficiently we get the right data into the processor when needed.

Everything we've discussed ties together: the contiguous layout, predictable stride, alignment, caching behavior—all enabled by understanding the mechanical sympathies and semantic choices in our code.