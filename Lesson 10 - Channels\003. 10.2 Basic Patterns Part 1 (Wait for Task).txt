Now that we have our signaling semantics in place, I want to share with you the three basic channel patterns that everything else is built on top of. After that, we'll look at more advanced channel patterns. I can't teach you how to become an expert multi-threaded software developer—this takes time and practice. What I can teach you are all the basic mechanics and semantics, and I want to continue stressing: don't add this complexity until you need it.

I want to go through these basic channel patterns first. We'll cover some higher-level patterns, and then we'll start looking at more code as we move forward through concurrency and the tooling that leverages everything we're discussing here. I would almost argue that if you're doing something that goes against the grain of these patterns I'm about to show you, it doesn't necessarily mean it's wrong, but you're probably adding a lot of extra complexity that you might not need. There are exceptions to everything, but maybe you don't need them.

This is the very first basic channel pattern around signaling, and it's called *wait for task*. The wait-for-task pattern is the foundational pattern we'll use for higher-level patterns like goroutine pooling. We'll leverage a wait-for-task type of pattern in various scenarios. Whenever we look at this kind of code, I like to think of goroutines as people—it helps me visualize the orchestrations taking place. I always imagine a manager and an employee: someone in charge and someone doing work on their behalf. Usually, when we're doing signaling, it's because we want someone to do something for us—either so we can do other things in parallel, or so we can potentially stop or correct them if things aren't going right.

This is our wait-for-task pattern. We'll see it again when we get to higher-level patterns like pooling. Here's how it works. The very first line of code on line 34 is the built-in function `make`. Remember, we use `make` for slices, maps, and channels—those are the three reference types in Go. There's no other way to initialize a channel and put it into its open state except through `make`, because you can't pre-initialize data inside a channel during construction. Literal construction doesn't exist for channels.

When we make a channel, we use the keyword `chan`, followed by the type of data that will pass through the channel. Data is always typed in this language, so channels are also typed. What you're seeing here is an unbuffered channel. I know it's unbuffered because there's no number specified. If there were a number—say, 10—it would be a buffered channel of size 10. But here, there's no number, so it's unbuffered.

But what I really want to focus on from line 34 is this: when I see this `make` call, I interpret it as creating a channel where we want guarantees that a send will be received. We want guarantees that the send has been received, and in this case, we're signaling with data—specifically, string data. Notice how I'm framing everything within the semantics we've discussed, moving beyond syntax and into meaning. This code is saying: we want guarantees, and we're going to signal with string data. Perfect.

The channel itself is nothing more than a pointer variable pointing to a very complex data structure underneath. Again, we're using value semantics throughout. This is the wait-for-task pattern. Here's the idea: I'm going to recruit a person, an employee, to go do some work for me. That's represented by the literal function and the `go` keyword that launches a goroutine. Here it is again—I've launched a goroutine to perform some work.

Imagine this scenario: always consider yourself to be the manager. This is our main path of execution. Here's the manager. Now, we go off and say we want an employee to help us do something. So we launch a second path of execution right here on line 36. This is our goroutine executing the function—that's us—and this is the employee goroutine on line 36.

Now look at line 37. On line 37, you see a channel receive, which is a unary operation. The arrow operator is attached to the channel variable. It's a unary operation, and this is a channel receive. So what we have here is a receive on our channel, and this is a blocking call—because we want guarantees. Essentially, we're blocked right here. We're saying, "Hey, employee, come here. Get involved. You now exist, you're moving. But I need you to wait." We're telling the employee to wait for us to tell them what to do.

How long will the employee wait? It's unknown. Remember, we have unknown latency here because we're after guarantees. We want to guarantee that this employee has received our signal. You wait—you don't know what to do yet. I'm going to send it to you. You wait. That means at this point, this goroutine is blocked, waiting for us.

In the meantime, we get to continue running in parallel. We proceed to line 41. Line 41 simulates some work we need to do—work that prepares the task for our employee. How long will we take on line 41? We don't know. I've even used `time.Sleep` with a random duration generator to simulate this unknown latency. We have no idea. They have no idea how long they'll wait for us, because we don't know how long this preparation work will take.

Eventually, we get past line 41, and now we reach our signaling phase—the send side. Notice that sending is a binary operation, whereas receiving is unary. We're going to signal with data, specifically string data. Our work is now prepared, and we're going to take this work and send it to the employee. They're waiting right here on the receive. We perform the send.

The syntax looks like this: the channel variable, followed by the arrow operator, showing that data is being placed into the channel. We do the send. Remember, we wanted guarantees. This send is blocking, waiting for a corresponding receive. And that receive is already in place.

Now we have both the send and the receive. To get the guarantee, the receive happens nanoseconds before the send. But both goroutines are in a blocking state. This goroutine is in a blocked, waiting state. The other has been waiting too. The only way forward is for this channel operation to complete.

What happens is that the receive occurs nanoseconds before the send—it starts first and is able to proceed. Once the receive completes, then the send completes. So the receive happens first, the send second—by nanoseconds. When we reach line 43, we have a guarantee that the receive has occurred.

Once this goroutine receives the data we sent over the channel, it can proceed to do its work. Right now, it's just displaying information, then it finishes, and we move on. We're running in parallel.

This gets confusing because the only atomic operation here is the channel send/receive itself—that's the only thing that's atomic. Understand that. I have two goroutines—Goroutine One and Goroutine Two—each running on their own core in parallel. Both enter a wait state when they hit the channel operation because they need the guarantee—that's what we asked for.

After that, these two goroutines run independently on their own OS threads. What does that mean? It means the print statements can technically happen in any order. There's no guarantee that the print on line 38 will happen before the print on line 43, because the only thing that's synchronized—within nanoseconds—is the channel operation itself.

This is why it's very difficult to use print statements to determine order. The only guaranteed ordering is that the receive happens before the send—by nanoseconds. If I build and run this program—just the wait-for-task example—I might see output suggesting the send happened before the receive. If we don't understand what I just explained, we might misinterpret that output. But that's not what actually happened. The timing difference is only nanoseconds. Both goroutines essentially resume execution at nearly the same time.

If I run this a few more times, I might eventually see the receive output appear before the send. There's no guarantee of that order in the output, but look—there it is. Suddenly, the receive appears to happen before the send. (Laughing.) This is exactly why people get confused with concurrency: they look at the order of print statements, but the only real ordering is atomic at the moment of send and receive. After that, all bets are off. You cannot rely on print statements to determine execution order.

I've left the print statements in precisely to demonstrate this point. When you're learning and working with concurrency, remember: your print statements won't help you. They'll only confuse you.

This is the wait-for-task pattern. Again, we'll use this foundational pattern for higher-level constructs like pooling.