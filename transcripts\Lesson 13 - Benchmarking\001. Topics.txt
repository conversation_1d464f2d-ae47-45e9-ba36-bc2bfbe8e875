Welcome to Lesson 13: Benchmarking. In this lesson, I'm going to start teaching you the basics of benchmarking and how important it is to validate any benchmark that you perform. We're going to focus on CPU and memory benchmarking, which represents the bulk of where you should be when looking to improve the performance of your software. This is where we're headed—where we're trying to make decisions about what performs better. But remember, we always optimize for correctness first. Once we have a running program, we can use these benchmarking tools to determine whether we're running fast enough, and if not, how we can improve. I'm going to show you how to leverage the output of these tools to make your code run better.