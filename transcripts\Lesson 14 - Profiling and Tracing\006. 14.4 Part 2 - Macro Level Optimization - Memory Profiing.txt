Go makes it very easy for us to get a memory profile out of a running Go program—we just need to set up a few things ahead of time, and then we can do that. I'm going to show you how to do this with a production-level project. I've already implemented these steps in the sample code we're running, but it looks a bit more polished when we examine it directly.

In my service repository, I have this service code here. Every one of my services does this, and if your service is going to run behind a firewall, you should do this too. It doesn't cost you anything until someone accesses it. However, if your service runs in front of the firewall, don't expose this, because you're giving away valuable information to anyone who knows it's there.

Now, I'm going to go into my `main.go` file for this service. I do this with all of my production software. Right here, you'll see a comment that says "Start the Debug Service." What we're doing is binding the `DefaultServerMux` to a localhost port. I'm using the server value here to bind the `DefaultServerMux` to a configured local port, and then I'm using `ListenAndServe` to host the `DefaultServerMux` on that port.

Here's the key part: once this is bound to a port, all I have to do is import `net/http/pprof`. Look at this—it's kind of cool. I import `net/http/pprof` using the blank identifier, which means there's an `init` function in that package that automatically binds several debugging routes to the `DefaultServerMux`. So when I come back and bind it, guess what? I now have endpoints that can give me CPU and memory profiles. How cool is that? This is all I have to do—just this one import—and I get these powerful debugging tools for free.

I've already done this in my other project, which means that the `/debug/pprof` endpoint already exists. Let me show you. My project isn't running right now—I hit Control+C. Let's run it again: `gctrace project > dev`. Now let's send a load of 10,000 requests through the server again and see how many garbage collections we end up with.

Actually, I just realized we're going to take a small performance hit on this run due to our cache state. So I'm going to run it again—let's do it twice to make sure we're in a steady state. I'll come back into the browser and hit our endpoint quickly: `localhost:5000/search`. I'm going to search for our favorite friend—there we go—just so we get some news. I'm guaranteed news with him. I hit Search, and we get a result. There it is. We know our data is cached now. We had two garbage collections during that warm-up.

Now let me run the full load again: 10,000 requests over 100 connections. We can see garbage collection running again. This time, we had approximately 2,380 garbage collections during this run. Let's look at our performance—I forgot to check it earlier. We're at 1,526 requests per second. So we achieved 1,526 requests per second at the cost of 2,383 garbage collections. That’s about 2,380 garbage collections for 1,526 requests per second. Let's remember that number.

Now, here's the great part: I already have a memory profile available. If I go back and navigate to `/debug/pprof` on my endpoint, how cool is this—there it is. I get this entire debugging interface for free, all because I bound the `DefaultServerMux` to a local host and port. This is the code you need: simply import `net/http/pprof` with a blank identifier, and boom—you get all of this.

You're looking at Go 1.11 output, which is really nice. With every new Go release, more features are being added here. You can even see helpful descriptions—like "a sampling of all past memory allocations." If we click on that, look: we have `/debug/pprof/allocs`. This is raw heap data—raw heap data from `pprof/allocs`. It's a full dump of allocations.

We also have blocking profiles, stack traces leading to blocking, command-line invocation details, and the number of goroutines. This tells us exactly how many goroutines are active in our program since the last refresh. Earlier it said 14; now it shows 8. We can see the program has released some goroutines it no longer needs.

This is our heap profile: a sampling of memory allocations and live objects. We can specify GET parameters and do various things with it. We can see everything here—mutex contention, all sorts of data. It's really amazing.

Now, the sampling of all past memory allocations is particularly interesting. If you were using Go 1.10, you'd only see the `/heap` URL. That was your alloc URL. It looked like a sampling of memory allocations on live objects. But in 1.11, they've separated the data: now we have one endpoint for live heap objects and another for all past memory allocations.

We're going to do a couple of things now. I want to use these URLs—`/debug/pprof/heap`, `/debug/pprof/allocs`—because they give us access to our heap profile. Let's do some experimentation.

I want to look at the heap profiles of this running program. It's already run, so we have a history of allocations—a sampling of all past memory allocations. We care about past allocations because we're looking for patterns and inefficiencies. I don't need to run more load. All I have to do is run:

```
go tool pprof allocs <endpoint>
```

Again, I'm using the `allocs` endpoint, and I can pass it the endpoint URL without the `/debug` prefix. This will fetch a profile over HTTP from our running program.

If I ask for the top 40 functions by cumulative allocation, look at what we get. I now have a breakdown of the top functions responsible for memory allocations in my program. I used the `allocs` endpoint because it shows past allocations. In Go 1.10, you'd use `/pprof/heap` for this, but in 1.11, they've split it: one for live heap objects, one for historical allocations.

I'm looking for low-hanging fruit—I want to find where we've done unnecessary allocations in the past. Here we are. When we look at the sorted list, it's not surprising that we start with socket connections and the mux—they're at the top of the call stack. We know we're rendering HTML, so that shows up too.

But here's what gets interesting: this line right here. This is the first time I see a function I wrote: `rssSearch`. Brilliant. You can see that out of the nearly 8 MB of cumulative allocations, more than half are coming from `rssSearch`. That's a clear indication that we might be over-allocating in `rssSearch`.

No problem. Let's look at `rssSearch`. I can use the `list` command: `list rssSearch`. Here we go. We get the source listing with flat and cumulative allocation numbers. It shows we have 4.7 GB of total allocations in this function.

If I scroll down to find the biggest contributors, oh my goodness—look at this. The bulk of the allocations are coming from line 119. If we look at line 119, I see a call to `strings.Contains` and `strings.ToLower`. It's cumulative, so the allocations are happening in one or both of these functions.

I don't want to guess which one is causing the issue, so look what I can do: I can use the `web` command to generate a visual call graph, and I can filter it to only show calls related to `rssSearch`. If I just run `web` on the entire program, I'd get a massive, unwieldy graph. I want to isolate anything that touches `rssSearch`.

When I do that, I get a clean, focused call graph. Let me show you why I used `list` first. If I just run `web`, the graph is much bigger—I'd have to search through it. I don't care about all of it. I only care about this part.

When I look at the filtered graph, I see `rssSearch` right here, with almost 5 GB of cumulative allocations. And what comes next? `ToLower`. `ToLower` is the culprit. It's because `ToLower` uses `strings.Map` internally, which causes allocations.

`ToLower` is my problem. Now I know what to investigate. Let's go back to the source code. I'm returning to our project's code—I need to look at the Go training code. Because I have a mental model of this code, I know the function is in `rssSearch`. Line 119—here it is.

Now I start to understand why we have so many unnecessary allocations. Look at what this code is doing: when searching RSS feeds for a topic in the description, it converts the entire description to lowercase. That causes an allocation because strings in Go are immutable. Every time we do this, we create a new string—and descriptions are large. These aren't just many allocations; they're large ones.

And I'm doing it on the search term too! I'm calling `strings.ToLower` on the term every time. This is crazy. What a waste. I really wrote this code—I wrote it for other purposes, but when I first profiled it like this, I couldn't believe what I was seeing. I couldn't believe I wrote code that inefficient.

Let's fix this. Here's the reality: we're caching the results after downloading them from the internet. This is the call to the cache on line 197. Couldn't we do something smarter?

I could use pointer semantics here since we're going to do some mutations. What if we convert the descriptions to lowercase *before* putting them in the cache? We should really have a separate field for the lowercase version—we should keep the original description intact and add a second field for search. But I don't have time to refactor that right now.

For now, let's make everything lowercase before it goes into the cache. That means all those extra allocations inside the loop go away. And it's especially silly to call `ToLower` on the search term repeatedly—we can do that once and reuse it.

Look at what I just realized: the profiler showed we allocated almost 5 GB of memory (not 500 GB—that was a slip) because of these `ToLower` calls, when it wasn't necessary. We can do these `ToLower` calls just once, fix the data at cache time, and eliminate thousands of allocations.

I had a syntax error—`string` instead of `strings`. Fixed. We'll do these allocations just once, before the cache. We won't feel them. We'll do the `ToLower` on the term once, before the loop, and now we're not over-allocating inside the loop. All of this is thanks to the profiler showing us where the extra allocations were happening.

This is pretty cool. Let me exit the profiler for a moment. Now, let's remember where we started: 2,381 garbage collections at 1,526 requests per second.

I'm going to hit Control+C to stop the server—that draws a line in the sand. I need to rebuild because I changed the code. Let's build it again. Now we have the new version.

I'll run it again—there it is, with tracing enabled. I'll draw that line in the sand again so we know where we were. Here we go—I'm sending 10,000 requests again. Boom. It's running, and this time it's using the new code.

Notice the garbage collection count is now down to 1,326—we were at 2,381 before. We just cut about 1,000 garbage collections by fixing that one issue. Let me find the start of the output—there we go. We were at 2,381, now we're at 1,328. We cut over 1,000 garbage collections just with that one fix.

I want you to notice the request latency hasn't changed—we're still in the one to two millisecond range. But we should be seeing much better memory behavior. Again, we don't have any memory leaks, which is great.

Now let's check the performance. We're now at 2,066 requests per second, up from 1,500. We added about 500 more requests per second just by cleaning up memory allocations. That's absolutely amazing. We did very little work, and I was able to pull the profile directly from the running Go program.

Look at what we achieved just by having those debug endpoints in place and using the `allocs` endpoint to see historical allocation data. That's how we found this low-hanging fruit and fixed it.

Now I feel pretty good about my memory situation. We added an extra 500 requests per second, even with all the work we're doing with HTML templating.

What would be nice to do next is get a CPU profile so we can look for algorithmic inefficiencies. We've optimized memory—now let's see if we can improve CPU usage using the same profiling tools we've already set up.