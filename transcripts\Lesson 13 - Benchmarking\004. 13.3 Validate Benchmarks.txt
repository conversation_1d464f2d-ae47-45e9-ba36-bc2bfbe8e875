One of the most important things you must do when benchmarking is validate that the results are accurate. I cannot stress this enough. Use your intuition if you have to, but you can't just blindly trust what you're seeing. Remember, we were just talking about how your machine must be idle, and there are many reasons why your machine might not actually be idle. I'm on my MacBook, and even though I've turned off most applications, there are still background programs running and network activity occurring. Additionally, your CPUs may have thermal throttling or dynamic frequency scaling that prevents them from running at full throttle all the time. All of these small factors matter when running benchmarks, and you must validate your results consistently.

There was an article I read once that used merge sort as an example. Merge sort is arguably one of the worst sorting algorithms to use in Go, due to how it works. You take a list—say, a million integers—and repeatedly split it in half until you reach the base case. Then, during the merge phase, you compare elements and combine the lists back up. This process creates an enormous number of allocations, making it an "allocation hell" type of algorithm. It's exactly the kind of algorithm you generally want to avoid in Go unless you're writing highly optimized code.

In the article, the author wanted to test merge sort performance under three different concurrency models: using a single goroutine, spawning a new goroutine every time the list is split (leading to an unlimited number of goroutines depending on input size), and limiting goroutines to the number of available CPU cores. The author asked: What's faster—using one goroutine, an unlimited number, or just enough to match the number of cores? On an eight-core machine, that would mean never exceeding eight goroutines.

The conclusion of the post was surprising: for merge sort, using only one goroutine was the fastest approach. I appreciated the underlying message—that throwing more goroutines at a problem doesn't automatically make it faster, especially without considering mechanical sympathy, which we've discussed before. But the result troubled me deeply. I couldn't believe that a single goroutine could outperform eight on CPU-bound work. My intuition insisted that some level of parallelism should be faster. I didn't understand it, so I decided to replicate the experiment myself.

I implemented merge sort—exactly as described in the article. I won't go over the code here; what matters is that I had a correct implementation matching the author's. I then wrote three benchmarks: one using a single goroutine, one spawning a goroutine at every split (unlimited), and one limited to the number of CPU cores (NumCPU). I replicated the entire setup and ran the benchmarks just as the author did.

I ran `go test`, executed all benchmarks with an increased benchmark time of three seconds, and focused solely on CPU performance, ignoring allocations due to their expected complexity. The results were striking: sorting a million integers with a single goroutine took 82 milliseconds, and the benchmark ran 50 times. With unlimited goroutines, it took 635 milliseconds—significantly slower. And when using eight goroutines (matching my eight-core machine), it took even longer than the single-goroutine version. This matched the author's conclusion exactly.

At that point, I could have accepted the result. My gut was wrong. Perhaps the allocation overhead was so severe that even parallel execution couldn't overcome it. Case closed. But I wasn't satisfied. I still didn't believe it. This is exactly what I mean by validation—you must validate your results. This result had bothered me for hours, and I refused to let it go. With my experience in multi-threaded software development and algorithm design, and with mechanical sympathy in mind, this outcome seemed impossible. I couldn't accept it without deeper investigation.

So I tried something simple: I ran only the NumCPU benchmark in isolation. The result changed dramatically. Suddenly, the eight-goroutine version dropped from 95 milliseconds to 55 milliseconds. Now, NumCPU was significantly faster—by about 30 milliseconds—than the single-goroutine version, which was exactly what I had expected all along.

What caused this difference? We have to return to the principle that your machine must be idle during benchmarking. When we ran the single-goroutine test, the system was relatively idle. The same was true for the unlimited goroutine test. But when we ran NumCPU immediately afterward in the same benchmark suite, the runtime was still cleaning up the massive number of goroutines created during the unlimited test. The scheduler and garbage collector were still dealing with the aftermath—goroutines that hadn't fully terminated, memory pressure, and scheduling overhead. That cleanup cost directly impacted the performance measurement of the NumCPU benchmark.

Only when I reset the system to an idle state and ran the NumCPU benchmark in isolation did I get an accurate reading. The true performance advantage of parallel execution, when done efficiently and with mechanical sympathy, became clear: it *is* faster than using a single goroutine on one core.

My point is this: you must always validate your benchmark results. You should have some understanding—or at least an expectation—of what the outcome should be. When reality contradicts your expectations, don't accept it at face value. Investigate. Make sure the anomaly isn't caused by measurement error, environmental noise, or flawed methodology. Ensure that what you're seeing reflects actual algorithmic performance, not artifacts of the testing process. True accuracy demands skepticism, rigor, and relentless validation.