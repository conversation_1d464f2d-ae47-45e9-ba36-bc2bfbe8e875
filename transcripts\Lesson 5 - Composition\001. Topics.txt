Welcome to Lesson 5: Composition. This is where we start focusing on design. At this point, we've learned our language mechanics. We've learned our language semantics outside of concurrency. But this is where you're going to spend the bulk of your time as a Go developer—at least I hope so—because composition is a huge and important part of writing software that won't become legacy code. We're going to learn how to design and build software from the concrete up and decouple things, and we'll also learn the mechanics around these concepts. So this is an incredibly important lesson for us to learn, because this is the work you're going to do every day. And I promise you that when <PERSON>'s time is over—and it will be, maybe in a decade or so—when the next languages come out, it is this lesson, this aspect of composition, that other languages are going to be copying from Go. That's how important this lesson is.