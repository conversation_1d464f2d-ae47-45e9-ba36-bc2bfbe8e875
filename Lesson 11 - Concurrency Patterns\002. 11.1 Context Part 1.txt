The Context package in Go is a very important package because it's where we implement cancellation and deadlines. Remember, your server can't take forever to do things—a request can't take forever, a task can't take forever, and neither can your database or network calls. If you allow operations to run indefinitely, you will eventually crash when conditions are bad. Timeouts are essential—timeouts for everything. The Context package is the solution to this problem.

Go made it very simple to create concurrent paths of execution using goroutines, but it didn't make cancellation as straightforward. That's why we need to learn how to use the Context package effectively. I'll show you that any API you write—especially those involving AI, I/O, or anything that eventually performs I/O—should accept a Context as its first parameter.

Another feature of the Context package is the generic value bag. I'm cautious about using this because you don't want to misuse it for things like request-local or thread-local storage. You should never hide data in a context. However, storing data in a context can be useful for secondary concerns—things that functions don't strictly need to operate, such as reporting metrics. We'll discuss this more, but the key point is that the value bag should only store non-essential, auxiliary data. In code reviews, if I see this being used, I'll make sure it's justified and limited to secondary purposes.

The value bag works by storing data in the context using a key-value pair. But the key isn't just any value—it's type-based. That means both the value and its type matter when storing and retrieving data. Let me demonstrate.

I've defined a type called `TraceID` based on a string—this represents the data we want to store. Imagine a web service generating a unique trace ID for each request. This is exactly the kind of information you might want to pass through the context. I also define a key type called `TraceIDKey`, based on an integer. This new named type ensures type safety.

I create a `TraceID` value—essentially a UUID represented as a string—and generate a key with the value zero. But this zero isn't just an integer; it's of type `TraceIDKey`. This distinction is crucial because the context uses both the value and its type to identify stored data.

To work with context, you need a starting point—a parent context. There are two ways to get one: `context.Background()` and `context.TODO()`. Use `TODO` when you know you need a context but aren't sure where it will come from or how it will be initialized. It acts as a placeholder, a reminder to come back and fill in the details later. I use `TODO` more often than `Background`, especially during early development when timeout values or context propagation aren't yet determined.

`Background` is the root parent context—an empty, initialized context with no data or deadlines. From our perspective, it's a clean starting point.

Context uses value semantics: when you pass a context into a function and modify it, you get back a new context. The original remains unchanged. This is important because a context can evolve as it moves through your program. For example, you might start with an empty `Background` context, then add timeout information before passing it to a function. That function might spawn two goroutines, each modifying the context differently—one might set a five-millisecond timeout while the other uses ten. Value semantics ensure consistency and isolation across these execution paths.

Because contexts are passed by value, when cancellation occurs—say, by calling `cancel()`—it propagates down all derived paths. You can cancel the entire tree or just a branch, depending on where the cancellation originates. This makes value semantics critical for maintaining predictable behavior across concurrent operations.

In our example, we start with the empty `Background` context and pass it to `context.WithValue()`, which adds state. We provide a key (of type `TraceIDKey`) and a value (our `TraceID`). The result is a new context containing everything the original had (in this case, nothing) plus our added data. We now use this new context going forward.

To retrieve the data, we call the `Value()` method on the context, passing in the key—again, with the correct type. On line 33, I pass the properly typed key (`TraceIDKey(0)`), and the method returns a copy of the stored `TraceID`. Because we're using value semantics, we're working with a copy, not a reference.

However, on line 39, if I pass just the integer value `0`—not the typed key `TraceIDKey(0)`—the lookup fails. The boolean flag (which we ignore with a blank identifier) would be `false`, indicating no value was found. If we had captured that value, it would be the zero value for the expected type. This demonstrates that retrieving data requires both the correct value and the correct type. Type safety prevents accidental collisions and ensures data integrity.

We must be very careful when storing data in the context. It's a generic mechanism, but we must never use it to hide information or create implicit dependencies. Everything stored should be transparent, well-documented, and limited to non-critical, secondary data like observability metrics. This is how the value bag works with `WithValue()`, and as you'll see, all context operations follow value semantics.

Now let me show you another API: `WithTimeout`. There are actually four `With` functions: `WithCancel`, `WithDeadline`, `WithTimeout`, and `WithValue` (which we just covered). For cancellation and timing, we use `WithCancel`, `WithDeadline`, and `WithTimeout`. They work similarly, so I'll focus on `WithTimeout`, and you'll understand the others by extension.

Here’s the idea: a goroutine starts some work, but it only has a limited amount of time to complete it. Let's walk through the code.

I set a duration of 150 milliseconds—the maximum time we're willing to wait. Then I call `context.WithTimeout`, passing in the `Background` context and the duration. I get back a new context and a function called `cancel`. This `cancel` function is critical: you must call it to release resources, or you will leak memory. The pattern is always the same—when you create a context with `WithTimeout`, `WithDeadline`, or `WithCancel`, you must also call `cancel`, typically via a `defer` statement.

It's safe to call `cancel` multiple times. Calling it triggers immediate cancellation—you don't have to wait for the timeout to expire. So deferring `cancel` ensures cleanup happens even if the function exits early.

Next, I create a buffered channel of size one to guarantee the sender won't block. Then I launch a goroutine—think of it as an "employee" performing work under the supervision of a "manager" goroutine. This is a fan-out pattern, though in this case we're only spawning one worker.

The worker performs its task—here, simulating 50 milliseconds of work—and then sends the result on the channel. This is the ideal path: the work completes quickly, and the result is sent and received.

Meanwhile, the main goroutine waits in a `select` statement, which allows it to monitor multiple channel operations simultaneously. In the first case, it waits to receive the result from the worker goroutine. This is the successful path—we want the send and receive to synchronize.

But in the second case, it also listens on the context's `Done()` channel. This is crucial: if the context's deadline expires before the worker finishes, the `Done()` channel will unblock, signaling a timeout.

Here's how it works: when we call `WithTimeout`, the context starts a timer. The `Done()` method returns a receive-only channel that will close when the timeout is reached. In our `select`, if the `Done()` channel closes before the worker sends its result, we know we've timed out.

In this example, the work takes only 50 milliseconds—well under the 150-millisecond deadline—so the result is received successfully, and we proceed.

But what if we change the work duration to five seconds? Now, when we run the program, we see "work canceled." Here's why: the 150-millisecond timer starts when we call `Done()`. Once that time elapses, the `Done()` channel closes, the `select` unblocks, and we handle the timeout case. The worker goroutine continues running, but we no longer care about its result. Eventually, it tries to send on the buffered channel—but since no one is listening, it blocks. However, because the channel has a buffer of one, the send succeeds, and the goroutine can exit cleanly. This prevents a goroutine leak.

This is the standard cancellation pattern: use `WithTimeout` to set a deadline, wait for either the result or the context's `Done()` channel, and clean up by calling `cancel`.

A deadline works similarly, but instead of a duration, you specify an absolute point in time. In practice, I find duration-based timeouts more convenient and use them more often than deadline-based ones. But the underlying mechanism is the same.

In summary, this is the core use of the Context package: storing values safely using typed keys, and managing cancellation and timeouts to ensure your programs remain responsive and resource-efficient.