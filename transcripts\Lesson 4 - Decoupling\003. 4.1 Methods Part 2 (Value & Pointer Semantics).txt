So now I want to give you some general guidelines on when to use value semantics and when to use pointer semantics. There are exceptions to everything—that's important—but if you don't know when to take an exception, don't. Engineering is all about choices, about cost, about knowing when to take those exceptions. These general guidelines are very good and will work for you most of the time, keeping you out of a lot of trouble. Remember, semantic consistency is everything.

Let’s start by looking at some code, pulling examples from the standard library. But before we dive into the code, let’s understand what we’re dealing with. In a Go program, there are really three classes of types we work with: built-in types (numerics, strings, and bools), reference types (slices, maps, channels, interface values, and functions), and user-defined types—primarily struct types. These are the three classes of data we must make semantic decisions about.

I’ll make this as simple as possible. If you’re working with built-in types—strings, numerics, and bools—you should use value semantics. I don’t want to see code that takes a pointer to an int, any numeric, a string, or a bool. Everyone should get their own copy. Built-in types, including fields in a struct, should be value-based. Strings, for example, are designed to be copied and are immutable by design—we’ve seen that before.

There are some exceptions. One example: if you're working with a SQL database and have a struct used for marshaling or unmarshaling data, the concept of "null" doesn’t exist unless you use a pointer. So if the struct represents a relational database schema and I see a pointer, we might need to talk. But when I say value semantics, I mean values are passed in and out of functions, and struct fields should also be values—not pointers.

Now, reference types follow the same rule: use value semantics. I don’t want to see you taking the address of a reference type. There is one exception: you may take the address of a slice or a map only if you're sharing it down the call stack to a function named decode or unmarshal. That’s it. If I see that, we won’t have a problem—it’s reasonable and practical. Decoding and unmarshaling require pointer semantics because they involve mutation. Slices and maps are collections of data, and since maps and channels are already reference types (underlying pointers), there’s no need to take the address of a pointer. The same applies to interface values and data structures. They’re designed to be copied and to stay on the stack. Therefore, fields should also respect value semantics—I don’t want to see pointers to slices or pointers to interfaces.

So two-thirds of the types use value semantics. This is fantastic. I’ve told you that value semantics are crucial for keeping your heap clean, which gives you tremendous performance benefits. But not all data can leverage value semantics. When it comes to struct types, that’s where things get interesting.

With struct types, you must choose which semantic is in play. You’ll hear me say this often: “What’s in play?” If you’re not sure what to use, default to pointer semantics. But if you’re absolutely certain value semantics are appropriate, then use them—they should be your first choice. Pointer semantics are the exception, used only when necessary.

Let’s look at some standard library code to illustrate this. Consider these two types from the net package, on lines 20 and 21: a new type named IP based on a slice of bytes, and a new type named IPMask also based on a slice of bytes. Now we have two brand-new types that are truly based on slices, meaning they are reference types. IP is a reference type, IPMask is a reference type. And what do I say about reference types? They follow value semantics.

Given that, we shouldn’t be surprised by line 26, where a method is declared using value semantics. Notice that Mask is a mutation API—so you might think, “Hmm, Mask needs to mutate; shouldn’t it use pointer semantics?” No, you’d be wrong. Remember: the code—methods, functions—must respect the semantics of the type. The type dictates the semantics, not the operation.

Mask is a value semantics mutation API. It receives its own copy of IP, mutates that copy in isolation, and returns a new copy. The mutation happens in a sandbox, so there are no side effects. This is safe and beautiful. The design of this API is driven by the fact that IP is a reference type, defined as a slice of bytes. Notice also that it accepts a value of IPMask—again, a reference type passed by value.

This principle extends beyond method receivers. Look at this function: it takes a value of type IP and returns a value of type string. Why? Because strings are built-in types with value semantics, and IP is a reference type that follows value semantics. You’ll see this pattern throughout the entire standard library. Nothing is random. The standard library follows these semantic rules strictly, with very few exceptions. You can verify this yourself.

This consistency is key to maintaining a codebase over time, especially with multiple developers. It allows the code to grow while maintaining predictability, reducing bugs, and enabling everyone to reasonably anticipate how the code behaves on the machine.

Now let’s return to struct types. When defining a struct, you must decide which semantic to use. Here’s the struct for Time, pulled from the time package. Let me ask you: if I asked you to write the Time API given this struct, which semantic would you choose—value or pointer?

You must choose the semantic before writing any code. Here’s a question that can help: if I give you a value of type Time, and I add five seconds to it, is it the same value slightly mutated, or is it a new value? Are two different points in time discrete pieces of data, or the same data modified?

I argue that adding five seconds creates a brand-new piece of data. When that’s the case, it’s usually a good sign that value semantics are appropriate—copies can be made, and mutations produce new values. We do this with strings: when you mutate a string, you get a new string. That’s a useful analogy.

Now consider a User type. Should we be making copies of users? This isn’t a technical question about safety—it’s about correctness. Are we optimizing for correctness, not performance?

Imagine I meet you and decide to call you Jack for the rest of the day, even though that’s not your name. Does that make you a different person? If I take a user value and change their name, is it a new user or the same user with a modification? It’s not a new person—it’s the same person with a changed name. That suggests pointer semantics are more appropriate.

Also, consider this: in the real world, we don’t make copies of people. Why would we do that in software? Our brains don’t expect that behavior, so it’s confusing. We should model software in a way that aligns with real-world intuition. Therefore, users should likely use pointer semantics—mutation modifies existing data, not creates new data.

This is a great question to ask when deciding semantics. It doesn’t always give a clear answer, but it’s a strong starting point. When in doubt, default to pointer semantics—it’s safer to share than to copy, because not all data can or should be copied.

Now, let’s see what the language team decided for Time. I’ve pulled the factory function from the standard library. Factory functions should always appear before the type declaration and are the most important functions in an API. Why? Because they tell you which semantic is in play—the one chosen by the developer.

Look at the return type of Now: it returns a value of type Time. You get your own copy. What does that tell you? Value semantics are in play. I’m creating a Time value and giving you a copy. You can share it, store it as a value, keep it on the stack when possible, and use Time values for your fields. Brilliant.

Factory functions are everything. This is where we start. This is how we choose our semantic. If you’re working with an unfamiliar struct—whether from the standard library or someone else’s code—look for the factory function. It should be right after the type declaration; it shouldn’t be hard to find. Use it to understand how to work with the data correctly.

Since value semantics are in play, look at Add. It’s a mutation API, but it uses value semantics—a value semantic mutation. This is not accidental. The code must respect the semantic, not the other way around. Add receives its own Time value, mutates it in isolation, and returns a new one. This ensures no side effects. It’s a beautiful design.

Time uses value semantics, and the API respects that. Here’s another function, Divide: it takes values of type Time, Duration (which is based on int64, a built-in type), and returns values—again, leveraging value semantics.

The reality is, you can look at almost any type in Go, identify its base type, and immediately get a sense of which semantic to use. Nothing should be random. Code with semantic consistency is always better than code with inconsistencies.

Here’s another example: a factory function for File in the os package. Notice it returns a *File—a pointer to File—not a value. What does this mean? Pointer semantics are in play. And here’s something critical: you do not have the right to make a copy of the value that a pointer points to when it’s shared with you. Assume it’s dangerous. Making copies of data behind a pointer is a major violation of semantic rules. Never assume it’s safe.

Look at the Chdir method (change directory). It doesn’t mutate the value used to make the call, but it still uses pointer semantics because the type requires it. The API, the code, must respect the semantic—not the other way around. The code doesn’t get to choose the semantic. We define the data, define the type, get the semantic, and then write code that follows it. This is critical.

To sum up:

- Built-in types: use value semantics.
- Reference types: use value semantics, except when unmarshaling or decoding—then pointer semantics are required.
- Struct types: you must choose. If unsure, default to pointer semantics.

Remember, you can choose a semantic at the beginning, later realize it was wrong, and refactor. We mostly work in closed-source systems—private codebases. Unless you’re in open source, you have the right to break APIs. We’ll talk more about design later, but I believe in breaking APIs when we’ve made a mistake. In versioned open source, we can’t break APIs, but we can add new ones and deprecate old ones to improve.

We must constantly review, adjust, refactor, and improve our code. Don’t get stuck because you’re unsure which semantic to choose. Pick one, be consistent, and if it’s wrong, refactor later.