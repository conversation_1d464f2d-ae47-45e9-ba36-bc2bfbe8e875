Hi, so we're now going to start learning about <PERSON>'s data structures. We're in the array section, but before we get there, let me share a little story. When I first came to Go from C#, and before that C++, one of the things that struck me as odd was that all my familiar data structures were gone. Where are my lists, queues, and stacks? These data structures I'd been working with for 20 years—suddenly they weren't there. All Go gives you is the array, this slice that looks like an array, and maps, which I can understand as key-value pairs. My brain started to shut down.

When I went to school in the late 80s, we were taught about arrays—but also taught to stay away from them. They were complicated, hard to work with, static in size, not dynamic. We learned to use linked lists and other data structures instead. I'd used them throughout my entire career, and now I come into Go and they're all gone. Arrays, slices, and maps—that's all I have.

When I first started coding in Go, I defaulted to using the linked list package because it was what I knew, how I knew how to handle data. But over and over again, I kept seeing slices everywhere in the language. I started to realize how deeply built into the standard library and built-in functions they were. The slice is a core data structure in Go, and today it's the most important data structure we have. But I didn't understand why.

I'm someone who really wants to understand what I'm doing and why I'm doing it—the mechanics and the semantics—so I can make better engineering choices. So what I want to do is share with you a small program that will help us understand why Go only provides arrays, slices, and maps, and why we should use these core data structures as our first choice. Going beyond them should be the exception, not the rule.

Let's look at this code. It starts by defining two constants: rows and columns, both set to 2000. We'll use these to create a matrix of bytes—about four million elements, each one byte in size. The result is a simple, large square matrix with rows and columns, each element being one byte.

On line 21, I've defined a data structure to simulate a linked list. A linked list is nothing more than allocating a small block of memory, putting your data into it, and using a pointer to point to the next piece of data somewhere else in memory. These were great data structures we learned—they're fundamental to many algorithms. Instead of allocating one large contiguous block of memory, we allocate smaller ones. Back when memory was limited, this was very important.

Our structure has a byte value (V), a pointer to the next node—making it recursive in form—and on line 27, we have the head pointer, which marks the beginning of the list.

In the init function, I do two things. First, I create a node in the linked list for every element—so four million nodes to match the four million elements in the array. Second, for every other element or node, I reset the byte from its zero value to 0xFF, because we'll be operating on every other node.

The core idea behind this code is to measure CPU performance: how much time it takes to traverse from beginning to end of both the matrix and the linked list. The linked list is unidirectional, so we're measuring how fast we can walk through the data. To add a bit of work, we increment a local counter on every other access.

When traversing a matrix, we can go in two directions: column-major order (going down each column) or row-major order (going across each row). It's the same block of memory, just traversed differently.

Notice something about the three algorithms: they all consist of essentially four lines of code. Four lines to traverse the linked list and increment the counter, four lines for row-major traversal, and four for column-major traversal. From an algorithmic perspective, in terms of lines of code, they all have similar efficiency. I can't reduce the number of lines, and I can't do anything more or less to make the code faster—I'm already at peak efficiency.

So we have two data structures and three different traversals: two directions on the matrix, one on the linked list. How do we measure how long these traversals take? Go's testing tool allows us to write benchmarks. We create an underscore test file—here named caching_test—and write three functions starting with "Benchmark," each taking a *testing.B parameter: one for linked list traversal, one for column traversal, and one for row traversal.

Here's how benchmarks work: the Go testing tool finds these functions and calls them one at a time. When it calls a benchmark function, it sets b.N to 1. All the code we want to benchmark must be inside the loop. It's crucial that the code inside the loop reflects real-world usage, or the benchmark won't be accurate. Also, for benchmarks to be accurate, your machine must be idle. I'm running this on my Mac, which isn't truly idle, but if you're doing long-term benchmarking—especially on a desktop—don't browse the web or watch cat videos. Leave the machine alone, because any other activity affects performance.

Another important point: when we run benchmarks, the Go compiler recompiles the code and may eliminate dead code. For example, if a function doesn't mutate anything and its return value isn't used, the compiler might decide not to call it at all, since it has no effect on the program. To prevent this, I assign the result to a local variable and then to a global variable, ensuring the compiler can't optimize the function away. We don't want a blazing-fast benchmark that's fast only because it did nothing.

By default, a benchmark runs for one second, meaning it may run for a few seconds as b.N increases. The first time it's called, b.N is 1; the next time, it's larger, and so on. The goal is to run the loop enough times—ideally millions—to get statistically confident results.

We have three benchmarks: linked list, column, and row traversal. Let's run them. I'll run them multiple times because my machine isn't idle, and I want to check consistency.

I'm in the terminal, already in the directory with these files. I'll ask the Go test tool to run the benchmarks. By default, it looks for test functions (those starting with "Test"), so I use the -run flag to filter them out. I pass a regular expression—here, "none"—to skip all test functions since there are none. But I want to run all benchmarks, so I use "." as the regex to match all. I'm also increasing benchtime from one to three seconds so it runs longer.

Let's run the CPU profile and see what the benchmarks show.

We have four million elements—or nodes—in our linked list. Starting with the linked list traversal, the time is 6.0 milliseconds. Column traversal takes 10.4 milliseconds, and row traversal takes 3.6 milliseconds.

This is already a fascinating result. Remember, row and column traversals use the same memory block—it's identical memory, not different allocations like the linked list. Yet the performance difference is dramatic: column traversal costs more than double that of row traversal, and the linked list falls somewhere in between.

Let me run it again to check consistency. The linked list is still around 6.0 milliseconds—actually slightly faster, from 6.08 to 6.06. But look at column traversal: it changed drastically—by nearly three milliseconds, which from our perspective is an eternity. Row traversal got a bit faster and remains very consistent. The linked list and row traversal are stable; column traversal is all over the place.

When I first ran this, my head was spinning. I saw similar results and asked myself: what's going on? My algorithms are as efficient as they can be, yet the performance differences—especially between row and column—are drastic. And with row and column, I'm using the same memory. I can't explain it by saying I'm accessing different memory locations—this is the same block.

So let me show you an animation from a great video by Scott Meyers on CPU caches and why they matter. Everyone should watch it. This animation shows the relative speed of accessing data from different caches: L1, L2, L3, and main memory.

Here's a level 1 memory transfer—very fast. Level 2 is slower. Level 3 is even slower. And main memory? Look how slow it is. Main memory is incredibly slow to access.

Let's watch it again. Level 1: super fast. Level 2: a bit slower. Level 3: still manageable. And then main memory—oh my god. I wish I had something to read while waiting. Boom—there it is. That's the relative speed of fetching memory from different cache levels.

How does this help us understand our results? Let's look at the hardware. This is the Core i7-900 series processor Scott used. It's representative of modern processors, so we don't need to dive into deep hardware specifics—we just need mechanical sympathy, an appreciation for how hardware works. With a caching system like this, our responsibilities are similar across systems.

The i7-900 is a four-core processor with two hardware threads per core. Each core has its own L1 and L2 cache. L1 is 64 KB, split between instructions and data. L2 is 256 KB, L3 is 8 MB, and then there's main memory.

The animation shows the relative clock cycle cost of accessing data from these caches to get it into the hardware thread's registers—that's the latency we saw.

Let me scroll down to see the actual numbers. To understand latency, assume the processor runs at 3 GHz. You may have noticed that processor clock speeds aren't increasing—they've been going down for years, largely due to heat constraints. But even as clock speeds decrease, we get faster instruction throughput. Today, we can execute about four instructions per clock cycle. That means on a 3 GHz processor, we should be able to execute about 12 instructions per nanosecond.

Based on that, the animation corresponds to these numbers: on the i7-900, accessing L1 cache costs 4 clock cycles—16 instructions or 1.3 nanoseconds. That's the best latency we can get.

If the data is in L2, it costs 11 clock cycles—44 instructions or 3.6 nanoseconds. Still acceptable, but slower.

If it's in L3, it jumps to 39 clock cycles—156 instructions or 13 nanoseconds. Reasonable, but a noticeable delay.

But if the data is in main memory, it costs 107 clock cycles—428 instructions or 35 nanoseconds of stall time on that core. That's an order of magnitude slower. And if this happens across all four cores, we could be stalling thousands of instructions because the data isn't in cache.

Main memory is incredibly slow to access, which is why caching systems exist—to reduce latency by keeping data close to where it's needed. Most modern hardware, including ARM processors, has similar caching systems, so these principles apply broadly.

An important detail on Intel platforms: data in L1 and L2 is also copied to L3. So you can't add the cache sizes together—L3 contains duplicates. From our perspective, the total usable cache is 8 MB. If performance matters, main memory might as well not exist. Taking a 107-cycle hit every time you access memory kills performance.

Two key points about mechanical sympathy: first, main memory is so slow that the effective memory you have is your total cache—here, 8 MB. Second, small is fast. If your data fits in cache and stays close to the hardware thread, you get better performance.

Now let's discuss how caching systems work, so we can tie this back to our results.

Suppose Core 0 needs to execute an instruction that requires the letter "M" in "main memory"—pretend that's our data. It would be inefficient for the cache to pull just one byte into L1. Instead, hardware divides memory into cache lines—typically 64 bytes. Some mainframes use 128-byte lines, and you can sometimes configure this in CMOS, but 64 bytes is standard.

All memory on the machine—virtual memory—is viewed by the caching system in 64-byte chunks. So when we need the byte containing "M," the entire cache line it's on is loaded into L1.

To make space, a cache line must be evicted from L1. The hardware decides which one—maybe it moves it to L2, which then requires evicting a line from L2. Since Intel processors copy cache lines to L3, we get some mechanical sympathy, but we still have overhead. AMD historically doesn't duplicate lines in L3, so you can add the cache sizes. Newer Intel processors like Skylake are changing this with larger L2 caches.

But from our perspective, the key is minimizing the 107-cycle latency of fetching a cache line from main memory into L1 or L2. We'd prefer the cache line already be in L1 or L2 when we need it. If it is, we only pay 4 or 11 cycles—we barely feel it. It stays within the core.

So how do we get cache lines into L1 or L2 before we need them? This becomes our responsibility. We must write code that creates predictable access patterns to memory—this is mechanical sympathy. If performance matters, we must be efficient in how data enters the processor, not focus on increasing clock speed. Predictable access patterns are everything.

How do you create predictable access patterns? The simplest way: allocate a contiguous block of memory and traverse it with a predictable stride. The prefetchers—small hardware units inside the processor—detect this pattern and start loading cache lines ahead of time. Prefetchers are critical, and we must be sympathetic to them.

The cleanest way to create predictable access patterns is to use an array. An array allocates a contiguous block of memory. When you define an array, you specify an element type—array of string, array of int—giving a predictable stride between indices. Prefetchers love arrays. Hardware loves arrays. It's the most important data structure from a hardware perspective. I almost don't care about your algorithm—arrays will outperform.

There are cases where data is so large that linear traversal isn't efficient, but for small data, arrays with predictable access patterns will win every time in traversal performance.

So the array is the most important data structure for hardware—but not in Go. In Go, the slice is the most important. Not because it uses an array underneath—technically, a slice is a vector. If you've watched C++ talks in the last five years, especially at CppCon, you'll hear speakers say, "Vectors, vectors, vectors—use vectors." Why? Because like slices, they use arrays behind the scenes, enable linear iteration, and create predictable memory access patterns that prefetchers can exploit. Brilliant.

There's another cache called the TLB (Translation Lookaside Buffer), also crucial. The OS manages memory in pages—typically 4K, 8K, 16K, or even 2 MB on some Linux systems. The TLB is a special cache that maps virtual addresses to physical memory pages.

Your program runs in virtual memory, thinking it has real memory, but it doesn't. When you access a virtual address, the TLB allows the hardware to quickly find its physical location in RAM. If there's a TLB miss—remember, it's a small, high-speed cache—the hardware must ask the OS, which traverses page tables. On virtual machines, this gets even more complex with nested page tables. A TLB miss can be devastating.

Predictable access patterns help not just with cache lines but also with TLB efficiency. Keeping accesses local ensures the TLB stays warm.

Performance today isn't about clock speed—it's about efficiently getting data into the processor before it's needed, minimizing latency, especially from main memory. Reducing L3 latency helps too.

Now, going back to our results: we can finally understand what we're seeing.

Row traversal wasn't just the fastest—it was incredibly consistent. Why? Because walking row by row moves through memory cache line by cache line in a predictable pattern. Prefetchers detect this and eliminate main memory latency. It's brilliant—this gives us the best possible performance on the machine.

But column traversal was slow and inconsistent. I designed the matrix to ensure that consecutive elements in a column weren't just on different cache lines—they were on different OS pages. Yes, they were far apart, on separate pages. This is essentially random memory access. That's why the results were so erratic and slow.

The linked list sits in between. We likely get cache line misses because the data isn't on a predictable stride, but the nodes are probably on the same page. That's why larger page sizes—like 2 MB—are useful for systems like databases with large memory footprints. More data per page means fewer TLB misses and better TLB cache performance.

So we're starting to understand: performance isn't about clock speed—it's about efficiently getting data into the processor, and that efficiency comes from creating predictable access patterns.

Now, when we look at what Go gives us—arrays, slices, and maps—it all makes sense. We don't have linked lists, stacks, queues, or other structures because they're not mechanically sympathetic with real hardware.

I'll never say anything negative about Java. The JVM is an amazing engineering feat—it handles mechanical sympathy for you with compacting garbage collectors and optimized data layouts. It ensures that even with object-oriented, linked-list-like patterns, predictable access is created under the hood.

But Go doesn't have a virtual machine. You're on the real machine. You're responsible for data layout and traversal. But that doesn't mean you're overwhelmed. If you use slices—the most important data structure in Go, not arrays—for most of your data needs, you're inherently creating predictable access patterns. You're being mechanically sympathetic.

And by the way, Go maps also maintain contiguous data underneath. Their job is to keep data contiguous. Think about it: stacks use contiguous memory, maps use contiguous memory, slices (as vectors) use contiguous memory. Predictable access patterns are everything.

This is why processor speeds can decrease while performance still improves.

So here's the takeaway: use slices as your first choice—specifically, slices of values—unless it's unreasonable or impractical. It might be impractical because your problem is easier to solve with a binary tree or linked list. If you say, "Bill, I'm not using a slice because this other structure makes the code simpler and easier to maintain," I'll say, "That's worth the small performance hit."

Because you've seen throughout this course that we prioritize integrity first. We accept performance hits for zero values, for low memory usage. So again, we aim to maintain mechanical sympathy—until it's not reasonable or practical to do so. And that's why Go gives us only arrays, slices, and maps.