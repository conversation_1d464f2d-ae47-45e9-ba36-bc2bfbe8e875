When you're ready to sit down and build a production-level project, Package Oriented Design will be your friend. I strongly encourage you to spend time reading the links I've provided on this page, as they go into great detail about the topics I'm going to cover. Package Oriented Design is critically important because it gives you an engineering decision framework for where packages should go, helps you identify how your Go project should be structured, and significantly improves communication between you and your team members. The core idea is to achieve clean, predictable package design and project architecture—something that is not arbitrary but consistent and easy to reason about. The concept of mental models is central here: knowing exactly where everything is. Without a deliberate Package Oriented Design guiding your project structure and packages, you're setting yourself up for problems. You might not see them today, or even a month from now, but over years—especially depending on team turnover—these issues will surface.

Interestingly, the history behind packaging offers valuable insight. In the year 2000, <PERSON> was interviewed and asked: "Can you tell us about the worst features of C, from your point of view?" Remember, this was 18 years ago. His response struck me so deeply that when I first read it, I almost fell out of my seat. He said: "I think the real problem with C is that it doesn't give you enough mechanisms for structuring really big programs, for creating firewalls in that program to keep the pieces apart. It's not that you can't do this, and it's not that you can't simulate it. You know, you can simulate OOP all you want—you can do that—but here's the key: you can simulate it, but the compiler and the language aren't giving you any help."

When I read that, it hit me hard. Here was <PERSON> <PERSON>igh<PERSON> pointing out that we have a language we're using, but as programs grow large, we face serious challenges around encapsulation and mental models. And Go's response is clear: "Brian, you're right—and we're going to fix this with the concept of packaging." In Go, packaging is our basic unit of compilation. It's how we structure and think about applications. The promise of Go's approach is fundamentally different from Object-Oriented Design—radically different.

For a long time, like many of you, I struggled with the idea of Package Oriented Design. So I want to share some guidelines and philosophies to help you start thinking about this from an engineering perspective. There isn't one single correct way to do this—I want you to understand that this is the approach I've developed over the past five years, and it's working well for me. But every project is different, every team is different, and team dynamics vary. I don't want you to feel locked into any one method. What matters is that you establish consistent project structures, philosophies, policies, and procedures that align with your goals. This isn't an "end-all, be-all" solution.

Much of what I'll share is widely accepted within the Go community, especially when it comes to language mechanics. But when we get into project structure, that's where it becomes personal—these are my opinions, my practices. You don't have to agree with them. But I do believe you should find an approach you can consistently adopt, so you maintain clear mental models of your codebase.

Let's now examine the language mechanics behind packaging in Go. The biggest challenge you'll encounter is that packaging directly conflicts with how most of us have been taught to organize source code. Traditionally, we're taught to create a folder somewhere in the project source tree, drop some code in it, and encapsulate different parts of the application. This stems from languages like C++, C#, and Java, where we typically build monolithic applications in terms of source code structure.

In Go, however, we don't build monolithic applications. In Go, every folder in your source tree represents a static library. Think about that for a moment. If we look at a project I'd like to share—my service project, available on GitHub under ardanlabs/go-training/service—you'll see what I mean. The import path is github.com/ardanlabs/service. We'll use this project to explore Package Oriented Design. It's a service project we're developing and using for training, designed to teach people how to write services in Go—the natural next step after this class.

Understand this: this is a project with a source tree. We'll discuss the folders, but each one—like the folder named "mid"—will ultimately become a static library. This is significant. Go is telling us that software is built by creating packages, which are APIs, which are effectively static libraries. The idea of building applications using static libraries isn't new—languages have supported this for years. C has .a and .so files, C# has DLLs, Java has JAR files, Ruby has gems. The novelty isn't the concept itself, but the fact that in Go, this isn't optional. You have no choice. Every folder becomes a static library, whether you like it or not. This imposes a real constraint.

You can't just create folders randomly and throw code into them, because doing so will hurt you. You must think about component-level API design and packaging from the very beginning. What's fascinating to me is that this source-level packaging makes me think of microservices at the source code level—where each folder represents its own unique API, its own program boundary, much like a microservice. So we need to find ways to decouple these boundaries and define clear contracts so they can interact. Again, this is about managing change. It's a fundamentally new way to think about software development, design, and architecture.

One important thing to understand: there is no real concept of sub-packages in Go. Just because a folder is nested inside another doesn't mean the inner package is a sub-package of the outer one. From the linker's perspective, all packages are compiled and laid out at the same level. The compiler sees no hierarchical relationships—no sub-packages, no parent-child dependencies. We can use the directory hierarchy to suggest relationships, but structurally, all packages are equal.

We've already discussed exporting and unexporting: exporting allows a package's API to be public. Another critical rule is that two packages cannot import each other—circular imports are not allowed. This is a deliberate language decision, made to simplify things and ensure consistent initialization. If two packages import each other, which one initializes first? That's a complex problem, especially when trying to maintain consistency over time. This restriction also forces you to slow down and carefully consider the coupling relationships between packages.

The language mechanics of Go's packaging system are relatively few, but they impose meaningful constraints on how we define, structure, code, and architect our projects. We're no longer building monolithic applications with loosely organized folders. Instead, we're building projects composed of many individual static libraries—packages—that are eventually imported and bound together. The more we can decouple these components, the healthier and more maintainable our projects will be.