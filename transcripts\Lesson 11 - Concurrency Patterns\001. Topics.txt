Welcome to Lesson 11: Concurrency Patterns. This is where we take everything we learned in the channels lesson and start exploring higher-level applications—things that are more practical. We’ll work on fixing a program that has a concurrency issue, leveraging channels, wait groups, and other concepts we’ve learned, to resolve the problem while maintaining a reasonable level of simplicity in the code.