Our next basic—and our last basic—channel pattern is "wait for finished." This pattern demonstrates how we can signal without data. I want to make it clear that what I'm showing you would be better handled with a wait group. Wait groups were designed specifically for signaling without data in orchestration scenarios and would result in cleaner code here. However, I'm going to walk through this example so you can understand the underlying mechanics. I emphasize that a wait group would be the cleaner solution, but seeing this implementation will help when we later discuss cancellation and deadlines with the context package.

Look at line 75: we're using `make` to create a channel of empty structs, and it's unbuffered. This gives us an unbuffered channel, with the only difference being that the data type isn't a string—it's the empty struct. We use the empty struct to indicate, from a code readability standpoint, signaling without data. Since the empty struct represents the absence of data, it's ideal for this purpose. Essentially, all we'll be doing is closing the channel. There's no actual data being sent, as I'll demonstrate. But from now on, whenever you see `make(chan struct{})`, you should immediately think: signaling without data. We're turning the lights off.

Let's go back and examine what's happening. We are the manager, running in our own path of execution. Right away, we launch the employee's path of execution—that is, a goroutine. Once we do that, we're asking this goroutine to perform some work. The goroutine already knows what it needs to do. Then we return and arrive at line 83. Notice that on line 83, we're performing a channel receive. This is very similar to what we've done before. Here, we're doing a channel receive, which means we're now in a blocking call. We're blocked right here because we're waiting on the channel receive operation.

I want you to notice something important about the channel receive operation. Up to this point, I've only shown you how to receive a value that's being sent. But when we talk about cancellation, deadlines, or signaling without data, there is no data to receive. That's why you're now seeing the second form of the channel receive. You can always retrieve data if you're signaling with data, but you can also receive a second value—a boolean flag—that I've named `wd` for "with data." This is my with-data flag, and it's part of the signaling mechanism. It tells me that if I successfully receive from the channel and the with-data flag is true, then actual data was sent—we signaled with data. But if the with-data flag is false, it means the receive occurred because the channel was closed—because the lights are out.

I don't actually need to store this variable; I'm just displaying it on screen in case you run this code. In practice, in these scenarios, we'd likely do nothing more than block on the receive, not caring about what comes out. But I'm showing you the with-data flag for clarity. So here we are, just as we discussed: I'm blocked, waiting. How long? I don't know—it's unknown. Even though we're signaling without data, we're still signaling with guarantees. I have no idea how long the wait will be. Eventually, the work finishes. This time, instead of sending data, on line 79 we close the channel. We turn the lights off.

When we close the channel, that close operation happens nanoseconds before the receive—similar to how a send must happen before a receive. The close completes first, and then the receive can proceed. The channel receive immediately unblocks once the close is complete. The close happens before the receive, and what we're effectively saying is: the work is done, you don't need anything else—turn the lights off. We detect the state change, and now we can move on.

Again, this is better accomplished with wait groups, and I recommend using wait groups in such cases. But when we get to cancellation and deadlines, you'll see examples where this exact pattern—using an unbuffered channel of empty structs to signal without data—becomes essential. We'll use it to signal to many goroutines simultaneously. If I had dozens of goroutines all blocked on the same channel receive, the moment I close the channel, all of them would unblock immediately and continue execution.

So this is our "wait for finished" pattern. We've looked at "wait for task," which is pool-related—the goroutine is idle, waiting for us to assign work. Then we have "wait for result," which resembles a fan-out or drop pattern: we launch goroutines that already know what to do, and we wait for them to send back the results of their work. And now we have "wait for finished," which applies to cancellation and deadlines. We use it to tell a goroutine, "Hey, stop what you're doing," or to signal on a large scale that it might be time to shut down.