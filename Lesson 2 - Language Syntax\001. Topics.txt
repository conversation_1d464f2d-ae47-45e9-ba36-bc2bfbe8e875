Welcome to lesson two, Language Syntax. This is where we begin learning how to read code in Go, with the understanding that every decision you make comes with a cost. Nothing is free—what benefit are you gaining for the cost you're incurring? This is where we start examining the cost of things. We're going to learn how pointers work and how memory works, which is critical if you really want to understand the cost of what you're doing. We'll explore how important it is to truly understand how things work at the level of memory. We're going to cover all of this, along with the language syntax that's essential for our ability to reasonably predict how code will run on the machine.