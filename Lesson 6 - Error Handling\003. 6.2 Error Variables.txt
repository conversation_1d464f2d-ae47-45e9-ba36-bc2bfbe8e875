Error variables represent our next level of context to give users an informed understanding of what is happening. These variables are particularly useful when designing a function that can return more than one type of error. If you look at the variable block I've set up, I've declared error variables using the errors package—specifically, errors.New—to create error interface values. However, I'm assigning them to global variables at the package level. Pay attention to the naming convention: we prefix these variables with "Err," such as ErrBadRequest and ErrPageMoved. This is an idiomatic Go naming pattern that you'll commonly find in the standard library.

These error variables serve as contextual signals. In the web call function, for example, if an error occurs, the function returns one of these predefined error variables. I recommend placing these error variables at the top of the source code file where they're used. If they're shared across multiple files, place them at the top of the main source file for the package. The goal is to make it easy for users of your API to discover these errors, which demonstrates that you care about their experience and are providing meaningful context.

When a developer calls webCall, they store the returned error interface value in a local variable. They then check whether a concrete value is stored in that interface. If an error is present, they can use a type switch or direct comparison to determine whether it's ErrBadRequest or ErrPageMoved. Based on which specific error variable is returned, the calling code can handle the error appropriately. This approach allows us to maintain the use of the default concrete type from the errors package—the unexported errors.stringError type with its unexported field—while still leveraging that type to provide sufficient context for informed decision-making.

Now we must consider the scenario where these error variables don't provide enough context. In such cases, the next step is to create custom error types. However, I strongly advise against creating custom error types unnecessarily. Avoid polluting your application with them just for the sake of customization. Instead, start by using the basic error string type and error variables, and only move to custom error types when the built-in approach no longer provides sufficient context.