Welcome to lesson six, error handling. If integrity matters—and it better matter to you—then error handling is everything. Everything we do in Go revolves around APIs; we design APIs, and error handling is about respecting the user of your APIs by giving them enough context to make informed decisions. Go is really special when it comes to error handling. It understands that it's not just about writing a program that works—it's about dealing with failure. This is what we do day in and day out. Engineering is about writing code that can handle failure gracefully without causing more problems. The error handling mechanics and semantics in the language are specifically tailored to that goal, and that's what we're going to learn in lesson six.