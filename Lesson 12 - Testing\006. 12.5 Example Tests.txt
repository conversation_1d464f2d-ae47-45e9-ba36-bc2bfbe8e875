So there's another kind of test in Go that a lot of people don't know about, and they're called Example Tests. In fact, examples serve two purposes: they help provide examples in the Go documentation that your code produces, and they can also function as tests. What I'm going to show you here is that I've added another file—an underscore test file—to this project called handlers_example_test. Inside it, I've written a function called Example. What's really interesting about this example is that there is no parameter being passed into it. We simply start with the word "Example," and then include the code we want to demonstrate. Since this is an example, we need to bind it to an actual exported function in this package. Notice that I have a function here called SendJSON—so this example is now named ExampleSendJSON. If I give it some other name, you'll see squiggly lines appear. The Go linters have identified that this example doesn't refer to any known exported item within the package. You can see that golint is helping ensure we follow this convention, and for good reason: if the example doesn't bind to something exported, it won't show up in the GoDocs. I'll show you the GoDocs in a moment.

Now, if you're documenting a method—something like a method on a type—there's a naming convention where you use the type name followed by an underscore and then the method name. So if this were a method on a type, we'd use the type name, an underscore, and then the method name. <PERSON><PERSON> will help guide you here. Okay, so notice it says ExampleSendJSON. Before we run this code and look at it, let's consider something a bit more real-world. I've switched over to the KIT project—the KIT pack—under ardanlabs, specifically looking at the log package. Notice that in log, I have log_examples_test. This is my naming convention for examples when I want to separate them into their own files. All you really need is a file with _test in the name, and it will be recognized. You can see that this example shows the user how to use the log API.

Now, what I want to do is run GoDoc. To run GoDoc, I just type `godoc` and specify which port I want it to listen on for HTTP. By the way, this is great because GoDoc can read your GOPATH and all the source code on your machine to generate documentation locally. So I've typed `godoc -http=:3000`, and now if I go to my browser and visit localhost:3000, this might look familiar. In fact, if I go to golang.org—the site we use for online documentation—it looks pretty similar, doesn't it? That's because golang.org is built around GoDoc, just with some modifications. What's nice is I can click on "Packages." This is especially useful when I'm on airplanes and don't want to pay for internet—I'm on planes all the time—so when I need documentation, I just run GoDoc locally and don't have to worry about golang.org being unavailable.

Another great thing is that I get a list of all packages—not just those in the standard library, but also everything in my GOPATH. Look at that—there's everything under my GOPATH. The one downside is that GoDoc doesn't include a search feature. So if I want to find the log package under Kit, I have to manually search. There's ardanlabs—right there—and I have a lot of stuff under ardanlabs, mostly Go training materials. This is the only frustrating part of GoDoc: finding what you're looking for, since I have to rely on the browser's search function. But here's Kit, and here's log.

You might find it interesting how polished the documentation looks here. Look: "Package log." Where did all this documentation come from? Where did this overview come from? The code you write is also documentation. Anything that's exported appears in the index, and any comments you write above exported items become part of the documentation. Look at this—see the "Example" section? That's all the code I wrote inside the example function. Here are some rules I follow: every comment you write is code, and it's also documentation. So comments must be proper sentences—with correct grammar, punctuation, everything. During code reviews, I check for this because every comment—especially those above exported items—shows up right here. Remember: your code is also documentation. Write clear, concise sentences with proper grammar and punctuation—the whole nine yards. You can see how the example code I wrote inside that function appears directly in the Go documentation.

Looking at part of the API, you can see why I emphasize clear, concise, and complete sentences. When you have a lot of overview documentation, the best practice is to create a doc.go file. You place all that overview documentation above the `package log` declaration. Any comments above `package log` become the package overview. And when you have a lot—say, 40 lines—don't put it in a regular source file. Just use a doc.go file, and make sure it's all about `package log`. How you space and indent things will affect how they appear—sometimes as code blocks, sometimes as section headers.

This is brilliant. So these examples become part of your documentation, and golint helps ensure your comments are present and useful from a documentation standpoint. But I started this by saying that an example is also a test—and I want to show you that. Notice that this example has at the bottom a comment with an "Output:" directive. That means any comment lines following it are what we expect to see on standard output. So anything this example writes to standard output can now be validated. Think about it: you have example code in your documentation. If someone says, "I don't know how to use this," you can say, "Dude, I've added examples to the GoDoc—just read them." But you've also validated that those examples work with every build of your code. So if someone copies and pastes that example, you don't have to worry about them coming back saying, "Hey, it's not working." No—because you know it works. That example produced this exact output, and you've tested it.

Look at this: we're asserting that ExampleSendJSON should always produce this specific JSON document. Now, let's test that. Let's go back to the handlers directory—I've got my example test—and I'll run `go test`, but this time I'll tell it to run only ExampleSendJSON. When I do, it just says "PASS," because the output matches. Now, let me change something. Let's pretend this was the expected output: just "bill ardanlabs." We know that's not correct, but let's say that's what we expected. Now watch what happens when I run the example. It says, "Hey, you got this, but you wanted this." This test fails. How brilliant is that? So I have examples that appear in Go documentation, and I can also validate that they work by testing what the example code outputs.

Now, let's fix the expected output back to the correct one. There it is. Run it again—brilliant. My examples are now passing. So examples are really powerful. But keep in mind: you don't have to use an example as a test. Examples don't have to be tests. When we looked at my log package earlier, there was no output directive—purely documentation. But if you want it to be a test, just remember that the output must be consistent. If your output changes based on time, location, or other variables, you can't use this approach. We shouldn't change code just to make a test pass—that's bad. Code is for users. We must focus on usability first. I don't want to add things to an API just to make it testable. But we can design APIs that are testable by focusing on clear inputs and outputs.

Here we have an example that appears in the Go documentation and can also be run as a test—killing two birds with one stone.