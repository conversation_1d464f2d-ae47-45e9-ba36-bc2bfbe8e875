Let's take everything we've done so far and summarize it in a live coding demo so you can see how these concurrency patterns—orchestration, buffered channels—can be leveraged to do things that would be much harder in other languages, but are now simple in Go, provided we maintain appropriate signaling semantics for the problem at hand.

Here’s the scenario: imagine we have an application that writes logs. This application uses the standard library’s logger, which I use all the time. I don’t necessarily need logging levels—I focus instead on signal versus noise in my logs. And while I do use structured logging when necessary, I avoid it when I don’t need it.

So here’s the app: let’s say it’s handling hundreds of thousands of video requests—streaming video to devices—and logging information about each device. We’ll simulate a program that uses the standard library logger, allowing all 10,000 goroutines to write directly to standard output. That sounds fine at first.

But here’s the problem: what happens if writing to standard output suddenly blocks? If every goroutine attempts to write a log and gets blocked on standard output, then every single one of them stops—enters a waiting state. That means no more video streaming. Literally, hundreds of thousands of customers on this server can no longer watch the game. And if it’s something critical like the World Cup—especially during a shootout—people are going to be really upset. I’d be upset too.

So let’s simulate this exact failure mode: a logging block that causes the entire service to deadlock all at once.

To do this, we need to mock a device where we can artificially induce blocking. I’ve defined a type called `device` with a boolean field named `problem`. When there’s no problem, streaming continues normally. When the problem is true, everything stops—the whole server halts.

Now, ask yourself: for your server and application, is it acceptable to stop operations just because logging fails? In the case of video streaming, logs are important, but you can’t stop the stream just because you can’t write logs. The video stream is far more important than the logs. That said, we still want logging to resume as soon as possible. We need to detect the issue, identify it, fix it, and keep moving.

I’ve seen applications where log volume is so high that you actually have to pause processing. But for video streaming? No way. We cannot halt the program just because logging is down.

So let’s simulate the problem. The `device` type implements the `io.Writer` interface—this allows it to act like a real device and work seamlessly with the `io` package and other parts of the standard library that expect writers.

In the `Write` method, if the `problem` flag is set, we enter an infinite loop, sleeping for one second at a time. This simulates a disk write block—some kind of I/O failure on standard output. If there’s no problem, we break out of the loop and write the log data to the device.

We’ve now created a mocked device that can simulate a blocking condition.

Next, we run the simulation using 10 goroutines. We create a `device` value using its zero value, and pass it to the standard library logger, which expects any concrete type that implements `io.Writer`. Since our `device` does exactly that, we’re good.

We launch 10 goroutines, each writing logs to the mock device and waiting 10 milliseconds before the next log. This simulates heavy logging activity across concurrent streams—like 10 users streaming the World Cup. And again, customers will be unhappy if the stream stops.

To trigger the simulated failure, I bind the program to the OS signal system and listen for `Ctrl-C`. We create a signal channel, pass it to `signal.Notify`, which hooks it into the OS. Now, every time I press `Ctrl-C`, we receive on the channel and toggle the `problem` flag.

Yes, there’s a data race here—this is a write, and elsewhere it’s a read, with no synchronization via atomics or a mutex. You’re absolutely right: this would be unacceptable in production code. But I’m omitting synchronization for clarity, so we can focus on the core concept. Just acknowledge the race exists—we’ll survive for this demo.

Each `Ctrl-C` flips the `problem` bit: no problem → problem → no problem, back and forth. Let’s see what happens.

I run the program. Ten devices are streaming the World Cup. I count to three and press `Ctrl-C`—simulating a standard output block.

One, two, three.

Look: the logging has caused the entire server to deadlock. All streams have stopped. Now, suppose we ran out of disk space. We clear it up—press `Ctrl-C` again—and the logs resume. But then the disk fills again, and logging stops once more.

This is unacceptable. We cannot stop streaming video just because logging fails. Relying on the standard library logger with direct writes from goroutines is not a viable solution.

We need to add complexity to handle this. Specifically, we need a way for up to 10,000 goroutines to write logs, but detect when a log write would block—and if it would, skip the log instead of blocking the goroutine.

How can we do this?

With the *drop pattern*.

Let’s sketch it out. What if we create a single goroutine whose sole job is to write to the device? That way, if the write blocks, we only have to track that failure in one place—one goroutine—instead of 10,000. That simplifies everything.

Yes, there might be a little latency in log delivery—but it’s logs. We can tolerate that. More importantly, we can detect when there’s a problem.

But how do we get 10,000 goroutines to feed log data to this single writer?

We use a channel. The goroutines can send log data through the channel to signal that there’s work to be done.

If we use an unbuffered channel, we’re in trouble—goroutines would block waiting for the receiver, introducing unacceptable latency. So we need a *buffered* channel.

The question is: what buffer size?

We’ll need to determine that through load testing and simulation. But a reasonable starting point is one slot per active goroutine—say, 10,000. It must be measurable and practical.

Why does this work?

Because we’re using the *drop pattern*. While the buffer has space, goroutines can send log data immediately—no blocking, no latency between send and receive. There might be minor contention on the channel itself, but it’s fast. As long as the buffer isn’t full, the system is healthy.

But if the writer blocks—say, due to a disk issue—the buffer will fill up quickly.

The drop pattern says: attempt to send data on the buffered channel. If it succeeds, great—the log will eventually be written. If it fails—because the buffer is full—we don’t wait. We *drop* the log.

We lose that log line, but the video stream continues uninterrupted.

And here’s the key: the full buffer acts as a *signal* that we can’t write to the device. Once the device recovers, the writer drains the buffer quickly, and logging resumes.

This is brilliant. We’ve turned a potential system-wide failure into a graceful degradation—dropping logs instead of stopping streams.

Let’s implement this.

I’ve created a folder called `logger` with a `logger.go` file. Inside, we define a `logger` package.

We need a `logger` type. Let’s define it as a struct. I’ll add comments to satisfy linters—exported identifiers need documentation in Go, and I don’t want red squiggles.

There are two core components: the dedicated writer goroutine and the buffered channel that feeds it.

Let’s start with the channel. For now, let’s make it `chan string`—log data is just strings. Simple.

Since we’re launching a goroutine, we also need a `sync.WaitGroup` to manage its lifecycle. The caller must be able to start *and* stop the logger—remember, you should never start a goroutine without knowing how it will terminate.

That’s all we need for now.

We’ll also need a factory function to create the logger. It should return a `*logger`—pointer semantics are required here because we can’t copy a `WaitGroup`, and we want exactly one logger instance.

The factory function needs two things: the destination device (anything that implements `io.Writer`) and the buffer capacity. Only the caller knows the expected load, so they must provide the capacity.

Now, we create the logger value. The `WaitGroup` is usable in its zero value, so no initialization needed. But we must make the channel with the specified buffer size.

We return the address of the logger. I use value semantics in construction (assigning to a variable), then return the pointer for clarity and readability. This also makes escape analysis more transparent.

Now, we launch the writer goroutine—its only job is to pull data from the channel and write it to the device.

We use a `for range` loop over the channel, leveraging closures for clean code. Each received message is written to the device using `fmt.Fprint`.

`fmt.Fprint` takes a writer (our device) and a value (the log string). Perfect.

So the goroutine receives log data and writes it to the device—formatted, if needed.

But we’re not done. We need full orchestration.

We call `l.wg.Add(1)` before launching the goroutine. When the goroutine exits, it calls `l.wg.Done()`.

This sets up the `WaitGroup` so that the caller can later wait for clean shutdown.

Now, we need a `Close` method to shut down the logger gracefully.

`Close` allows the API user to terminate the writer goroutine in an orderly fashion.

How? By closing the channel. That breaks the `for range` loop, which causes the goroutine to exit and call `Done`.

Then we call `l.wg.Wait()`—blocking until the goroutine confirms it’s done.

That’s it. Close the channel, wait for the goroutine to finish.

Important: the caller must ensure no goroutine tries to log after calling `Close`. But that’s manageable—any well-designed system should coordinate shutdown properly.

Now, one final API: the `Print` method.

This is what replaces the blocking `log.Print` call. We can’t let this block.

So we implement `Print` as a method on `*logger`, taking a string.

Inside, we use a `select` statement.

We attempt to send the log string on the channel:

```go
case l.ch <- v:
```

If it succeeds, the log is queued. The writer goroutine will eventually process it.

If the send would block—because the buffer is full—we hit the `default` case.

In the `default` case, we *drop* the log. For visibility, I’ll print "drop" to the console so we can see it happening.

We’re no longer blocking. We’re dropping logs, but the video stream continues.

After just 49 lines of code, we have a complete, robust logging API. The only added complexity is a buffered channel and one goroutine.

Now, let’s integrate this new logger into our app.

First, import the package. I’ll use a relative path and alias the package name to `log` so I can reuse the familiar API.

The only change needed is in the logger construction. Instead of using the standard library’s `log.New`, we use our `logger.New` function, passing the device and buffer capacity—initially set to the number of goroutines.

We might need to adjust the capacity later based on load testing. We want to avoid false positives—situations where the buffer fills quickly not because of a real failure, but due to a temporary spike. But starting with the number of goroutines gives us a solid baseline.

Now, I’m nervous—did we get this right?

Let’s run it.

Switch to the console and start the program.

Wait—I just noticed: our logger doesn’t add line feeds. The output will look messy.

Let’s fix that quickly. I’ll modify the `Print` method to append a newline.

Back in the code—new logger in place.

Build and run.

We see logs flowing—video streaming, everyone watching the World Cup.

Now, simulate the disk problem: one, two, three—press `Ctrl-C`.

Look: we get “drop” messages. We’re hitting the `default` case. The logs are being dropped—but the video keeps streaming!

Now, fix the simulated problem—press `Ctrl-C` again.

The buffer clears, logging resumes.

We’ve detected the failure, degraded gracefully, and recovered—automatically.

This is engineering.

This is the power of Go’s channels: they give us orchestration and signaling semantics that let us detect problems, respond appropriately, and recover—without complex machinery.

Forty-five lines of code—and we’ve made our system resilient.

Our customers stay happy. They keep watching the game.