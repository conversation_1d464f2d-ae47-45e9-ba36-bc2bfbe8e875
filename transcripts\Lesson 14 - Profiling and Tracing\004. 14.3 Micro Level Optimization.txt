So I want to show you how to leverage benchmarks to do micro-level optimizations. I call this micro-level because we're focusing on a single function, trying to make it run faster either by examining CPU changes or by leveraging memory more effectively.

Here's the basic idea: One day, I decided I wanted to learn more about the io package. I don't often work with streaming data, and everyone talks about how great the io package is, so I thought, "I want to learn the io package." I came up with a somewhat crazy scenario: Let's pretend that a stream of bytes is coming over the wire, and somewhere in that stream is the name "<PERSON>." The idea is that <PERSON>' name should always be capitalized—after all, he's the <PERSON>, and we can't have his name on the internet without a capital E. So this program will scour the internet byte by byte and fix any instance where the <PERSON>'s name isn't capitalized.

I created a data table of input and output. The input is a stream where "elvis" might appear with a lowercase 'e'. If it does, like in this case, it should be changed to "<PERSON>" with a capital 'E'. The goal is to find his name regardless of surrounding spaces and ensure it's properly capitalized.

I have some functions here that take the input and output streams and create a single stream from them. In main, I have a small program to run and validate the results. Here is my algorithm. Let's walk through it.

I start by creating a NewBuffer from the bytes package, with my input loaded into it. I determine the length of what we're searching for—in this case, five, since we're looking for "<PERSON>." Then I had this idea: create a Buffer of that size—five bytes. I try to read in the first four bytes. If I can't, obviously there's no chance of finding "<PERSON>." Once I have at least the first four bytes in the buffer, I enter an endless loop where I read one byte at a time using the io package. Once I have five bytes, I compare them. If there's a match, I write out the replacement—"<PERSON>" with a capital 'E'—and then read in the next four bytes. If there's no match, no problem—I just keep going, shifting bytes through the slice. I had no idea what I was doing when I wrote this; I just threw it together in about 20 minutes to play with the io API. But it worked, and I was pretty happy with it.

Then I reached out to the Go community and asked, "How else would others solve this same problem?" My friend Tyler, who's in Salt Lake City—hey Tyler, brilliant Go developer—said, "Bill, I've got some time. I'll give you a solution." Tyler came up with a different approach. He didn't use NewBuffer; instead, he used NewReader—a reader from the bytes package. He also identified the number of bytes we're looking for, but then he did something clever: he created an index position. He reads one byte at a time from the input, compares that byte with the corresponding character in "Elvis" using the index, and increments the index. If he gets five consecutive matches, he writes out the replacement. If at any point the match fails, he simply writes out the current byte, unread it, and starts over. It was a really elegant solution.

When I compared his solution with mine, I built the program and ran it. Both algorithms worked. I now had two distinct solutions to the same problem—proof that there are many ways to solve a problem. I asked Tyler, "Did you benchmark your algorithm? I'd like to see whose runs faster and who's allocating more." He said, "No, Bill, I didn't. I just wrote it and sent it to you." So I decided to write some benchmarks.

I went into my _test file and wrote two benchmark functions: algorithmOne and algorithmTwo. AlgorithmOne is mine; algorithmTwo is Tyler's. I do all the setup before the benchmark loop, and critically, I call `resetTimer` before the loop. This is important—if you do significant work before the loop, you must reset the timer to ensure accurate CPU and memory measurements. So I reset the timer, then run the loop.

I ran the benchmarks with `go test -run=none -bench=. -benchtime=3s -memprofile=m.out`. Let's see what happened.

My algorithm (algorithmOne) ran about two million times, taking 2453 nanoseconds per operation. It resulted in two heap allocations totaling 117 bytes. Tyler's algorithm (algorithmTwo) ran about 10 million times—almost five times faster—at 525 nanoseconds per operation, and it had zero allocations. I was blown away. I called Tyler—me in Miami, him in Salt Lake—and said, "Dude, do you realize you wrote a zero-allocation algorithm out of the box?" He said, "No, Bill, I had no idea." That's how good Tyler is. We were both dancing in our offices—we were thrilled. This doesn't happen often.

But then I hung up and suddenly felt deflated. All my happiness turned into depression. I teach this stuff every week, and I didn't write a zero-allocation algorithm. I wanted to know where my allocations were coming from. Could I make my algorithm faster while keeping its core logic intact?

I wanted to understand where those allocations were occurring. When optimizing for performance, we always go after allocations first, then look at CPU-level algorithmic efficiencies. So I needed a profile—a runtime memory profile of this benchmark.

I could generate it from the command line using the `-memprofile` flag and specifying a file, say `m.out`. So I ran the benchmark again with `-memprofile=m.out`, which produced a memory profile.

Now I had an `m.out` file. How do I use it? I use the `pprof` tool. `pprof` lets us analyze CPU, memory, and blocking profiles. We have a memory profile. The default view for memory profiles is `inuse_space`, which shows only what's currently in the heap when the profile was taken—what's in the live heap. But I don't care about the live heap right now. I want to see every allocation that ever occurred, whether it's still in the heap or not. For that, I need `alloc_space`. There's also `alloc_objects` if you want to count allocations, but I'm using `alloc_space`. So I override the default with `-alloc_space`.

Now I run `go tool pprof -alloc_space m.out`. The profile contains all the necessary symbols, so even though the binary name might not show up, the function names and source are available.

We already know Tyler's algorithm has no allocations; mine does. I want to see where mine are coming from. So I use the `list` command and ask the profiler to find any function with "algOne" in its name.

Sure enough, it finds `algorithmOne`. Because the symbols are in `m.out`, I get a full view of the source code, with annotations showing where allocations occurred.

There are two columns: `flat` and `cumulative`. The `flat` column shows allocations directly caused by that line of code—allocations the line itself is responsible for. The `cumulative` column sums up all allocations from that line down through the entire call stack.

Looking at the data: out of 328 MB of total allocations, 318 MB came from inside `NewBuffer`. Ten MB came directly from that line (`flat`), and since there are no further allocations below it in the call stack, this line and `NewBuffer` are the sole sources.

So we know something in `NewBuffer` is causing most of the allocations. Eliminating these should improve performance. But here's the limitation of the profile: it shows *what* is allocating, not *why*. We need to understand the reason behind the allocation.

For that, we need the escape analysis report—ideally, inline with the profile. So I tried to exit the current `pprof` session, but I couldn't—Control-C wasn't working. So I killed the terminal tab, restarted, and re-ran the benchmark.

This time, I added `-gcflags="-m -m"` to the `go test` command. This tells the compiler to produce a detailed escape analysis report before running the test. I showed this earlier when discussing pointers, stacks, and memory.

I cleared the terminal, ran the test again. First, the escape analysis output appeared, then the benchmark ran. I cleared everything and re-entered `go tool pprof -alloc_space m.out`, then `list algOne`.

The numbers are slightly different due to re-running, but the pattern is consistent: most allocations are still attributed to `NewBuffer`. The `NewBuffer` call is in `stream.go` at line 83.

I searched for `stream.go:83` in the escape analysis output. At the top of the report, I found the relevant line. It says the call to `NewBuffer` is being *inlined*.

Wait—what? Inlining? That seems unrelated to allocations. But it's critical. Inlining is an optimization where the compiler takes the body of a function and inserts it directly at the call site, eliminating the function call overhead.

Why is this in the escape analysis report? Because inlining affects escape analysis and allocation decisions.

Recall that values escape to the heap in two main cases: when they're shared beyond their scope (e.g., returned from a function or passed to another goroutine), or when the compiler doesn't know their size at compile time.

Look at `NewBuffer`. It's a factory function that returns a pointer to a `Buffer`. That means it's using pointer semantics—constructing a value and sharing it up the call stack. Normally, that would cause an allocation.

And indeed, the escape analysis shows: construction of the `Buffer`, then `&`—the address is taken and returned. So it escapes to the heap.

But the escape analysis also says: "inlining call to NewBuffer." That means there's no actual function call—the code is inserted directly at line 83.

So if there's no function call, how can there be a *cumulative* allocation? The allocation should be *flat*—local to line 83—because the `Buffer` is constructed right there, not inside `NewBuffer`.

The profiler, however, still shows it as *cumulative*, likely because it doesn't account for potential optimizations like inlining. It reports based on the original call structure.

So technically, the allocation is flat—it's happening at line 83 due to inlining. But the profiler doesn't reflect that nuance.

We know the `NewBuffer` call didn't actually happen—it was inlined. But there's still an allocation, and we don't yet know why.

Let's go back to the escape analysis report. Scrolling down, I see more output. It says: `&bytes.Buffer literal . does not escape`—wait, no: actually, it says the `bytes.Buffer` literal *does* escape.

Why? Not because of `NewBuffer`—that's gone due to inlining. But because of an interface conversion on line 93: "pass to call argument escapes."

Let's check line 93. It's a call to `io.ReadFull`. The first parameter is of type `io.Reader`—an interface. We're passing our concrete `*bytes.Buffer` (`input`) into this interface parameter.

And what did I say repeatedly in this course? Decoupling comes with a cost: indirection and allocation. When a concrete value is stored in an interface, it must be allocated on the heap.

Ah—there it is. The allocation occurs not in `NewBuffer`, but when `input` is passed to `io.ReadFull` and boxed into an `io.Reader` interface.

That explains it. And if it hadn't already escaped, this call would have caused the escape.

Why doesn't Tyler have this allocation? Let's check his code. He calls `NewReader`, gets a `*bytes.Reader`. But he never passes it into an `io` function that takes an interface. Instead, he calls methods directly on the value—using its method set. He stays in the concrete world. No interface boxing, no allocation.

He avoids the decoupling cost.

And if we check the escape analysis report, we see that `NewReader` is also inlined—same optimization. Search for line 134: "inlining call to NewReader." Yes, there it is. So Tyler benefits from both inlining and avoiding interface allocations—hence his zero-allocation result.

Now we know our problem: passing `input` to `io.ReadFull` causes the allocation due to interface conversion.

But we can fix it. I'm not tied to using `io.ReadFull`. The `input` variable—a `*bytes.Buffer`—has its own `Read` method. In fact, it's identical to `io.Read`.

So instead of `io.ReadFull(input, ...)`, I can call `input.Read(...)` directly—using the concrete method set.

I can do the same for the other `io.Read` call. And for the single-byte read, instead of a generic read, I can use `input.ReadByte()`.

`ReadByte` returns the byte and an error. I'll adjust the code accordingly.

I'm maintaining the spirit of the algorithm—still reading byte by byte, checking for "Elvis"—but now using the concrete methods instead of interface-based `io` functions.

This change alone should eliminate the interface allocation.

Let's run it again. I've made the code change. Run `go test -gcflags="-m -m" -run=none -bench=. -benchtime=3s -memprofile=m.out`.

We get the escape analysis report. Now, looking at the benchmark: we're down to one allocation—the slice—and we've shaved about 1000 nanoseconds per operation. We're now about three times slower instead of five. That one allocation was costing us huge performance.

This is why we target allocations first.

But we still have one allocation left. Let's profile it: `go tool pprof -alloc_space m.out`, then `list algOne`.

The remaining allocation is for a slice on line 88. Search for `stream.go:88` in the escape analysis.

It says: "does not escape to heap" — wait, no: "escapes to heap" because of a "make slice" with "non-constant size."

This is a new message in Go 1.11. In 1.10 and earlier, it said "too large for stack" or similar, which was misleading. Now it correctly says "non-constant size."

And that's the key: the compiler doesn't know the size at compile time because we're using a variable `size`. Since the size isn't a compile-time constant, the backing array can't be stack-allocated—it must go to the heap.

So if I hardcode the size to 5, the compiler *can* know the size at compile time, and the slice might stay on the stack.

Let's try it. I'll change `make([]byte, size)` to `make([]byte, 5)`.

Run the benchmark again.

Now we have zero allocations. And the performance is just under three times slower than Tyler's.

Notice how those two allocations were responsible for roughly 1600 nanoseconds per operation? That's massive.

I can't leave the hardcoded 5 in production, but for optimization purposes, it proves the point.

Now that we've eliminated allocations, let's do a quick CPU profile to see if we can improve algorithmic efficiency while keeping the same logic.

Run: `go test -run=none -bench=. -benchtime=3s -cpuprofile=c.out`.

This generates a CPU profile. Now run `go tool pprof c.out`.

No special flags needed for CPU profiles. Type `list algOne`.

Out of nearly four seconds of cumulative time, the biggest chunk is in the `compare` function.

So if I want to speed up my algorithm further while preserving its spirit, I need to reduce the cost of the comparison.

Tyler doesn't use `compare`—he uses byte-by-byte indexing and direct comparison. That's why he's so fast.

This is micro-level optimization using benchmarks: identify a function you care about, benchmark it, profile memory and CPU, use escape analysis, and iteratively improve.

One last thing: the `pprof` tool has a new feature since Go 1.10. You can run `go tool pprof -http=:8080 c.out`, and it opens a web UI.

It gives you a visual call graph, flame graphs, top lists—very helpful for beginners.

The UI is improving with every Go release and makes profiling more accessible.

I prefer the command line—I'm faster with it, and I like combining escape analysis with profiling. But the UI is great for learning and sharing.

For the rest of this course, I'll stick to the command line, but feel free to experiment with the UI.

This is micro-level optimization using benchmarks.