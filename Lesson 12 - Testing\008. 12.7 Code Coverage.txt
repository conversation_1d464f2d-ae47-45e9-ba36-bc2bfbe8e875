I want to show you how the built-in code coverage tooling works in Go. This is really brilliant stuff. In one of the earlier sections, I asked you how you know when you're done with a piece of code, and one of the things we discussed was code coverage. I believe you need to achieve somewhere between 70 and 80 percent code coverage overall before you can say you're done. I do expect to see 100 percent coverage on the happy path, but I want that 70 to 80 percent overall. But how can I verify that? Let me show you how it works.

I'm back in my kit project, using the TCP package to demonstrate how we can implement code coverage. Notice I have two test files here that allow me to test this API, and the API itself spans three files: client.go, tcp.go, and tcpconfig.go. We can perform basic code coverage by navigating into that folder—I'm already here in the TCP directory—and running `go test` with the `-cover` flag.

As you can see, the result shows only 58.3 percent code coverage, which is insufficient because I've already stated that below 70 percent, you're not done. Maybe you shouldn't listen to me, since I have code out there without proper coverage. This kind of thing happens when you're moving fast to add features and tell yourself you'll come back later to add tests. That's exactly why we need checks and balances.

Now, 58 percent is useful to know, but we don't know where that coverage is actually occurring. What we need is a coverage profile. By generating a coverage profile, we can pinpoint exactly which parts of the code are covered. Instead of just using `-cover`, I'll use `-coverprofile` and write the output to a file—let's call it `c.out`. This command generates a `c.out` file containing the coverage profiling data.

But how do we examine this coverage? We use `go tool cover`. There are many Go tools—we'll look at pprof and tracing soon—but this one is `go tool cover`. I want an HTML representation so I can view it in the browser, so I'll run `go tool cover -html=c.out`. That's all—just `html c.out`.

Look what happens: a browser window opens, and now I can see the code alongside its coverage status. There's a dropdown that lets me switch between files to see coverage at the file level. Right now, I'm looking at client.go, which shows 93 percent coverage. I can switch to tcp.go, which is at 52 percent, and config.go at 53 percent.

Let's focus on tcp.go—this is unacceptable. It should be at least 70 percent. Why aren't we there? Scrolling down, I immediately see the issue: this is the implementation of the error interface for the client error type, and I have no tests validating this code. That's not good.

On the other hand, the `new` function has excellent coverage—everything is green. Remember, I want 100 percent coverage on the happy path, and this tool helps me verify that by showing green lines. Edge cases that are unlikely to execute are acceptable, but look at all this code here—these are edge cases related to socket failures that I might never trigger, and that's okay.

But here's something completely unacceptable: there's a public API method called `done`, and I haven't written a single test for it. If I were your manager and you came to me claiming you were done with this, I'd send you straight back to your desk. This is not acceptable.

But here's the good news: now that I can see the gaps, improving coverage almost becomes a game. I can write tests and watch the lines turn green—green, green, green—and keep running the coverage report to track progress.

It's really powerful that Go gives us this capability. We can use the `-cover` flag for a quick summary, or generate a coverage profile with `-coverprofile`, then use `go tool cover -html` to visualize the results. I strongly encourage you to ensure you're hitting that 70 to 80 percent coverage threshold. Use this tooling to identify what's missing, determine what you should be adding, and make sure you've done the job properly before telling anyone you're done.