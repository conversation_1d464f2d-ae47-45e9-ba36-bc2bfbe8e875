Variables—alright, so in this section, we're going to talk about variables. But trust me, this material assumes that you're already a software developer and that you know what variables are. I'm not teaching you variables from the very beginning. These are just the things I need to cover to help us build a foundational knowledge as we continue to grow in complexity in our ability to read and write code in Go. So I really hope you stick with this section. I'm not teaching you what a variable is, but there are some very important concepts here.

We'll now scroll down into the code review area. We'll be using the Go Playground for much of this. One of the key things I want to talk about is type. Type is everything. Type is life. And I promise you—one day, if I get a little too drunk, I'm going to go to a tattoo parlor and tattoo "type life" right on my knuckles—because it's that important. Actually, if that ever happens, you can laugh at me for the rest of your life. But seriously: type is everything. Type is life. Without type, we can't have integrity, and we can't truly understand the cost of the decisions we're making in our code.

Start with this: our basic unit of memory in the programming model is a byte, and a byte is made up of bits. I've just drawn a byte on the board with a specific bit pattern. Now, what I'd like to ask you is: what is the value of this byte? What value have I stored inside this box? The reality is, you cannot answer that question unless I give you the type information—unless you know what this bit pattern represents.

If I tell you this represents an integer, then you can say, "<PERSON>, that represents the number 10"—brilliant. But if I say no, it doesn't represent an integer—it represents a color—then you have to ask, "Bill, what is number 10 on your color chart?" And I might say, "Oh yeah, that's red." You can see that this same bit pattern can represent an infinite number of things—it all depends on the type information.

Type provides us with two pieces of information: size—how much memory, how many bytes we need to read or write—and representation—what the data actually represents. Go isn't being novel in many cases; you likely already know the syntax of this language, so I don't have to teach much syntax. Like other programming languages, Go has built-in types: numerics, strings, and booleans.

What you're seeing on lines 15 through 18 in the code is exactly that—the built-in types being used. Look at the names of the types. Start at line 17—I love looking at line 17. The type name float64 tells us two things. I want you to look at some of this early material differently than you ever have before. I'm not necessarily teaching you anything new—I want to give you a different perspective on what's happening here, so we can build a solid foundation as things get more complex.

When you look at the name float64, it's interesting because it gives us both parts of the type information. The "64" tells us it's an eight-byte, 64-bit value—this gives us the memory footprint, the cost. And "float" tells us it's an IEEE 754 binary floating-point number. You see how the name itself provides both pieces of type information.

A bool is one byte—we only need one bit, really, for on/off, true/false. Then we get to int. There are precision-based integers: int8, int16, int32, and int64. But if I saw you using a precision-based integer and it wasn't obvious why, that would raise a flag for me during a code review. Now, raising a flag doesn't mean it's wrong—it just means the reason isn't obvious, and I'd want to talk about it. There are valid reasons: maybe you're using atomic instructions, or you need the size to be consistent across platforms. But most of the time, we're just going to use int.

This can seem confusing because the type name "int" only gives us half the type information—it tells us the value represents an integer, but not the size. And if we don't understand size, we don't understand cost. So how much memory is allocated for an int? That depends on the architecture we're building for.

Most of you, if you type "go version" into your command line, will see that you're using AMD64 as your architecture. Some of you might be on ARM, but for the most part, you're probably on AMD64—meaning you're on a 64-bit architecture. Let's stick with AMD64 for a moment. From a Go perspective, working on a 64-bit architecture means our pointer size—or address size—is 64 bits, or eight bytes.

Here's how Go works: we look at the address size for the architecture we're on—AMD64, 64 bits, eight bytes. Then we define our generic word size. When you hear me use the term "word" throughout the class, I'm referring to a value that can change size depending on the architecture. So on AMD64, our word size is 64 bits or eight bytes. Go then says: if your address size and word size are 64 bits or eight bytes, let's make the integer the same. So all three—address size, word size, and int size—are always the same.

This gets interesting when you look at code on the Go Playground. The Playground is a single-threaded environment, so we won't be able to use it much when we get to concurrency. But another thing about the Playground is that it runs on an "amd64p32" architecture—a Native Client architecture—where addresses are 32 bits. For all intents and purposes, it's a 32-bit architecture because the address scheme is four bytes or 32 bits. That means addresses are four bytes, word size is four bytes, and therefore, our int is four bytes or 32 bits.

So we see that int can change size depending on the architecture, and there are mechanical sympathies for this. The hardware or architecture is essentially saying that four-byte integers are more efficient on a 32-bit system, and eight-byte integers are more efficient on a 64-bit system. We're trying to align with the platform's efficiency when it comes to address size, word size, and integer size. That's why we prefer to use int instead of a precision-based integer—unless there's a specific, obvious reason to do otherwise.

There's another concept I want to mention—one of Go's idioms: there are too many ways to declare variables and create values in Go. We'll talk about this early in the class. What you're seeing here is that I'm using the keyword var to declare these variables.

There's a concept called zero values—and it's very important. Zero value is an integrity play in Go. I want us to understand that the cost of integrity is performance. This little performance hit will never show up in a profile—the machine is very fast—but we want it. Zero value means that all allocated memory is initialized to at least its zero value state. Any time we allocate a byte of memory and don't pre-initialize it with a value, the system ensures it's set to zero.

This helps prevent a huge number of bugs we've seen in the past: a program starts, allocates a variable, doesn't initialize it, the bit pattern happens to be reasonable, and the program runs—but it's in a corrupted state. Zero value is an integrity safeguard, and it's very important.

One practice I follow in this codebase is this: if I'm declaring a variable and it's going to be set to its zero value, I use var. Var acts as a readability marker that we're declaring and initializing to zero. And var guarantees zero value 100% of the time in Go. I'll show you later that if you're not using var, you might not get zero value.

Now, there's one more type: string. String is a somewhat artificial data type in any language, and there are different ways to implement it. Go has what I'd call a unique approach—I hadn't seen it before Go. On line 16, when we write "var b string," we're declaring a string and setting it to its zero value—an empty string.

An empty string in Go looks like this: b is a two-word data structure. Strings are two words. I'm using the term "word" because the size of a string changes with the architecture. On the Playground, a word is four bytes, so two words is eight bytes. On a 64-bit system, it's two eight-byte words—16 bytes total. The first word is a pointer—we'll talk about pointers later—and the second word is the number of bytes. Since this is an empty string, there's no pointer to a backing array yet, and there are no bytes. So a string is a two-word data value—eight to sixteen bytes, depending on architecture.

Again, I want to talk about cost—engineering cost—so we can reasonably predict how things will run, how they'll behave in memory. Type is important. The size of things can be important. We really want to understand the cost of what we're doing.

Now, you won't always want to create, construct, and initialize to zero value. There will be times when you want to pre-initialize something. That's where the short variable declaration operator comes in—the ":=" you see on line 27.

Many people think this syntax comes from C, but actually, the Pascal programming language also had a strong influence on Go's syntax. This is one of those places where Pascal's influence shows.

This operator is a productivity feature—it lets us declare and initialize at the same time. I don't want to suggest that Go has duck typing—it doesn't. It's struct-based typing. But in this case, I want you to see the short variable declaration operator as a productivity tool. So aa is 10, bb is a string, cc is a float, dd is a bool—all inferred from the value on the right-hand side, because the compiler knows the type.

Going back to "hello"—if we had a string "hello," it would be an array of five bytes: H-E-L-L-O. The pointer in the string header would point to that array, and the length field would be five. We'll go deeper into this as the class progresses.

I really believe in visualization—being able to visualize code. Early in my career, I kept a piece of paper and a pen at my desk and would draw these things out. What you're seeing me draw on the board are my mental models—my visual representations of how code works. You'll see a lot of that throughout this class, and I think it helps. It might help you too.

So we use the short variable declaration operator when we're not initializing to zero value.

You might see code like this—using ":=" to declare an integer and set it to zero. There's nothing technically wrong with that. A big part of writing good code and building good mental models is consistency. If you want to do this, be consistent. I wouldn't. I'd use var for zero value because the readability signal of var is too strong to ignore. If I were doing a code review, I'd ask you to switch it to var—but only for readability and consistency. So I don't do that, and you won't see it in this codebase. But it's up to you—as long as your team is consistent, it's fine.

Now, there's one more concept I want to talk about: conversion. Go doesn't have casting—it has conversion. And conversion means we may incur a memory cost when converting values from one type to another.

If you've never heard of casting, traditionally it's been used to improve performance by saying: "I allocated a one-byte integer—here it is. But for some reason, I want to treat it as a four-byte integer." Casting allows you to tell the compiler: "You know a is an int8, a one-byte integer, but treat that memory as if it's a four-byte integer." The compiler trusts you and says, "Okay," and suddenly you can read and write across four bytes from that location—which could corrupt a lot of memory.

This is a silly example, but casting traditionally comes up in two places. One is when dealing with data coming over the wire—say, a large number of bytes. You might want to copy those bytes directly into memory and then overlay a struct on top of them via casting. That's very fast—you just say, "Those 20 bytes starting at this address represent this struct," and then you can access them as such. But if you're off by one byte, you're reading and writing memory you shouldn't be. That's a real problem with casting.

Go says: integrity is number one. We care about integrity above all. You can use the unsafe package to do casting, but we'd rather have conversion. Go's stance is: we don't want to set up that scenario. If you really want a to be four bytes, we'll convert a into a new four-byte value—maybe even give it a new variable name, like aaa in the example.

The idea of conversion over casting is another integrity play. There might be a cost—new memory allocation—but we'd rather be safe than sorry.

So here's what I want to share in this section: you already know what a variable is. But to sum up: type is everything. Type is life. It gives us two things—size (how much memory we allocate and access) and representation (what that memory means). Without type, we have chaos. Type gives us the integrity we need.

We have zero values in Go—an integrity feature. Use var when initializing to zero value. All memory is initialized to at least its zero value state. Use the short variable declaration operator when initializing to a non-zero value. There are exceptions to every rule, and part of engineering is knowing when to make exceptions—but these are the guidelines we'll follow.

And finally: conversion over casting. Again, it's an integrity play—to keep our software, our data, and our memory safe.