The last mechanic we need to learn before we can get into design is exporting. Exporting is kind of like the encapsulation piece in Go, but it's not the same as private and public in object-oriented programming languages—it's a little different. There are concepts around packaging that we need to discuss, but I want to save the broader packaging conversation for later when we talk about design. For now, let's focus only on what we need to understand about exporting.

Let’s go back and use the VS Code editor to illustrate this, because we need to talk a bit about packaging. The basic unit of compilation in Go is a package. A package in Go represents a folder. I want to go deeper into this later, but for now, understand that encapsulation in Go begins and ends at the folder level. Every folder represents a static library, and that is our smallest unit of compilation.

Here, I have a folder under example one called *counters*. This folder also serves as the namespace for everything inside it—that is, everything in this package. I also prefer to have one file named after the package name; that helps with clarity. We’ll discuss that more later.

Given this basic unit of compilation, the idea is that symbols are either exported—meaning they’re available outside the code in this folder—or un-exported, meaning they’re only accessible to code within the same folder or package. Up until now, I’ve been careful to keep everything lowercase because <PERSON> emphasizes convention over configuration. This is one of Go’s more brilliant conventions.

Notice that the folder name is *counters*, and the package directive is also *counters*. You really should match the folder name with the package name, because we import packages by their folder name. If these names don’t match, you’ll confuse people—this is the namespace, and that’s the folder path, and we expect them to align. All code inside this folder must use the same package directive, or the compiler will complain.

Even though the compiler doesn’t strictly require the folder and package names to match, tooling can break, and you’re not being a good Go citizen if you don’t follow this convention. So the name should be short and meaningful. We’ll talk more about naming later.

Now, look at the type I’ve declared: *AlertCounter*, starting with a capital A. In Go, you simply look at the first letter of any named identifier. If it starts with a capital letter, it’s exported—it’s available outside this folder or package. If it starts with a lowercase letter, it’s un-exported and only accessible within the package.

Since *AlertCounter* starts with a capital A, it’s exported. I can now go into the example Go code here. You can see a long import path pointing from the Go path to this folder or package. But don’t do this—please avoid using relative paths like this. It creates messes for you and everyone else. Instead, import the package using its folder name. Since we’ve matched the namespace with the folder name, we already know how to use it.

Here, I’m taking a literal integer value of 10, converting it to a value of type *AlertCounter* from the *counters* package, and assigning it to a variable. Everything works because *AlertCounter* starts with a capital letter, so it’s accessible—it’s been exported from the *counters* package. That’s our form of encapsulation.

But what if, in the *counters* package, we used a lowercase *a* instead? Then *alertCounter* would be un-exported. Only code inside that folder or package could access it. If I try to use it from outside, I’ll get an error. The compiler will say, “Sorry, I can’t access this un-exported name—it’s undefined.” So we get levels of encapsulation based on naming.

But understand: this isn’t about private and public data. It’s about the identifier—whether the named thing is accessible to the developer outside the package. Let me show you an example.

Here’s some code I wouldn’t write in Go. If I saw this in a code review, we’d fix it—but it’s still instructive. I have an un-exported type named *AlertCounter* (lowercase *a*) and an exported function named *New*. It’s common to have factory functions called *New*. But look at what *New* does: it takes an integer, converts it to the un-exported *AlertCounter* type, and returns a value of that un-exported type.

This is interesting because how can we return a value of an un-exported type if we can’t access that type directly from outside the package? It’s true that you, as a developer, can’t use the un-exported type directly—but the compiler can. When we call *New*, the compiler is able to declare a variable of the un-exported type. We’re not getting real encapsulation here, so please, please, please don’t write code like this.

But this example shows how exporting is different. It’s not about the data—it’s about whether the named identifier is directly accessible to you, the developer, outside the package. The compiler always has the power to create variables of any type, exported or not.

There’s another interesting point. Let’s look at this: I’ve changed the package from *counters* to *users*. The file is named *users.go*, matching the package name, which also matches the folder name. Now I want to show you type-level encapsulation.

Previously, we saw package-level encapsulation. Now, here the type *User* is exported, with two exported fields (*Name* and *ID*) and one un-exported field (*password*). That means only code inside this package can access the *password* field when working with *User* values. Outside the package, only *Name* and *ID* are accessible.

This code doesn’t build because I’m trying to access the un-exported *password* field during construction. The compiler says, “This field is unknown.” So you get field-level encapsulation just by capitalizing or lowercasing the first letter of the field name.

One last interesting example: back in the *users* package, I’ve defined a type named *User* with a lowercase *u*—so it’s un-exported. Only code inside this package can use the *User* type. But it has two exported fields: *Name* and *ID*. Then I have an exported type called *Manager*, with an exported field *Title*, and I’m embedding an un-exported *User* value.

There’s no real benefit to this setup, but here’s the question: once I’m outside the package and I have a *Manager* value, which fields can I access? Here’s the cool part—because of inner-type promotion, I can access *Title*, *Name*, and *ID*. The fields from the embedded *User* are promoted.

The problem is, during construction, I can’t initialize the inner *User* value because the type name starts with a lowercase *u*. We’re not achieving real encapsulation because all the fields are promoted anyway. If I saw this in a code review, I’d change *User* to start with a capital *U*.

However, it’s very common in Go to have un-exported types with exported fields. That’s because Go’s marshaling and unmarshaling mechanisms only work with exported fields. So you’ll often see types where the type name is un-exported but the fields are exported.

I want to be very clear: this isn’t a private/public data mechanism. It’s about whether the named identifier—the thing you’re trying to access—is available to the developer outside the package. Remember, the compiler can always create variables of any type, exported or not, because it has that power.