Let's get into a little bit of design now, because one of the things I find lacking in a lot of Go code is logging consistency. I spent 30 years trying to figure out how to combine error handling with logging, and I finally came to the realization that for those 30 years, I shouldn't have been trying to combine them—I should have been trying to separate them. I always wanted to separate error handling from logging, both in terms of frameworks and logic. But I eventually realized that you can't separate these two things. Error handling and logging are one and the same, and we have to bring them together if we want any consistency.

I'm always worried about consistency. That's why I love asking this question in the trainings I do: imagine it's three o'clock in the morning and an error occurs in your software. The error is handled—the program continues running. There's no real hiccup; maybe there was a brief pause, but it recovered and kept going. My first question is: do you want to be woken up at three in the morning because this happened? Remember, the software didn't need you—it handled the failure on its own. The reality is, I don't want to be woken up. The software did exactly what it was supposed to do: it identified a failure, recovered, and kept moving. Let me sleep.

But when I wake up in the morning and get to work, I do want to see some indication that there was a problem. I don't want to be blind to it. However, the real question becomes: am I actually going to spend time looking at the logs when I get in? Right now, the issue is resolved—it was recovered. Maybe on my dashboard or metrics, I see that an error occurred, but I don't know much more. The program kept going. The reality is, I'm probably so busy with meetings and other priorities that I never go back to look at the logs. It's not that I don't want to, or that I'm not curious—it's just human nature. We have other priorities.

What I'm trying to stress is that we write applications that log a lot, and most of the time, we're logging as an insurance policy—to be able to find bugs when errors occur. I did that for a long time. But the reality is, our systems today have too much activity. User bases can grow to a million people overnight, and logging at that scale has a huge cost. Logging often creates a large number of allocations, which puts pressure on your heap. This isn't unique to Go, but since we're talking about Go, I want you to consider that logging is important, but we must constantly balance signal versus noise.

If you're writing logs—writing data to your logs—that you never read or use, you're wasting CPU cycles on something that could have been actual work. And it goes beyond just CPU cycles. You're consuming network bandwidth, disk I/O, and introducing other system-wide complexities. During development, I want to ensure we have a high signal-to-noise ratio in our logs. We should log the bare minimum needed from a tracing perspective, but when we log errors, we must include enough context so that if we need to investigate later, we have what we need.

That's been a big problem for me for 30 years: how do you ensure there's enough context in the log—both from a tracing standpoint and an error standpoint—without duplicating errors, while minimizing log writes, and at the same time establishing a consistent pattern that everyone can follow and review during code reviews? A pattern where logging isn't random? It's overwhelming. Honestly, my head hurts thinking about it.

You might find it hard to believe, but I don't believe in logging levels. I've never been able to turn up a logging level in time to get more detailed information when I needed it. So I'm not a believer in logging levels. I tend to use the standard library's log package quite a bit. I'll create my own logger and pass it around the application, but for me, it's binary: either I need the information in the log, or I don't. I make sure through unit testing and integration testing that my logs contain real signal.

Now I want to show you a pattern using Dave Cheney's errors package. It's excellent and gives us a consistent way to apply error handling, logging, and code consistency—minimizing a lot of pain. But remember when I said you can't separate logging from error handling? What do I mean by "handling an error"?

To me, error handling means that when a piece of code decides to handle an error, it takes full responsibility. That means logging the error with full context. It also means making a decision: can we recover, or not? If we can't recover, then error handling means shutting down the application—either via a stack trace from a panic or an OS exit. If we can recover, then the code must restore the application to a correct state and keep it running. And when that function returns, it never returns another error up the call stack. The error stops there. Either the app is recovered or it shuts down—and we've logged the error. That is error handling to me. And this pattern with Dave Cheney's errors package is perfect for exactly that.

Imagine this code: we're at the bottom of a call chain, and we call `thirdCall`. Some function above us calls `thirdCall`, and you can see that `thirdCall` always fails. It returns a custom error type called `AppError`. We're using pointer semantics, storing the address of `AppError`, and we've set the error code to 99. This is similar to how standard library functions return raw error values.

`secondCall` is the one that called `thirdCall`. You see the traditional Go mechanics: we use an if statement to check for an error, avoiding else clauses, keeping the happy path first. We call `thirdCall`, get back an error interface value, and check if there's a concrete value stored inside. The answer is yes. Now the developer must make a choice—boolean, really: do I handle the error here, or not?

If the answer is yes, we handle it: we log it, decide on recovery, and stop the error from propagating. But if the answer is no, there's only one thing you're allowed to do: wrap the error with context. We prefer to handle errors as low in the call stack as possible, because that gives us the best chance for recovery. But in this case, the developer decides not to handle it.

Think about this: I no longer have to worry about logging or recovery. All I worry about is the wrap call. And the wrap call is powerful—it does two things. At this point, we already have an `AppError` wrapped in the error interface. Now, when we wrap it, we add two types of context: call stack context and user context.

Call stack context captures exactly where we are in the code—the line number and call stack at the point of the wrap. User context lets us add meaningful information. In this case, I'm indicating that `secondCall` was calling `thirdCall`. So we get both call stack and user context built in.

We take this wrapped error and send it back up the call stack. Now `firstCall` is involved. `firstCall` called `secondCall` with a parameter `i`. An error occurred—we know `secondCall` returned an error. Now `firstCall` must decide: do I handle the error? If not, again, only one choice: wrap the error again, adding more context.

This time, our user context includes the parameter values we passed into `secondCall`. This is brilliant. You can share any context you need. If you get a bug and the context isn't enough, that's what you improve—the context. If I'm writing a database application and it's safe to log queries (no security risk), I'll log the actual query so I can copy and paste it from the log and run it directly. I love showing input values because they won't appear in the call stack, but they're invaluable for debugging.

So here we have the original error, one wrap, another wrap, and now we're in `main`. `main` calls `firstCall`, gets back the error, checks if there's a concrete value in the interface—and there is. Now, the mechanics I showed earlier—generic type assertions—still work, thanks to the `cause` function.

What's brilliant about `cause` is that it knows how to unwind the stack of wrapped errors and get us back to the root error value. Once we have that, we can type assert against it and handle it—like on line 30. But even more powerful is this: if you're using the standard library's `fmt` or `log` packages, and you use `%+v` in your formatting, the full stack trace and all context get logged—whether to standard out or standard error. If you use just `%v`, you only get the user context. `%v` gives you the user context; `%+v` gives you both.

I want to show you this running so you can see it in action. Let me copy the path and go to the terminal. I'll build and run the program. Watch the output.

The first output uses `%v`—you see just the user context. Then we use `%+v`—and now you see the full stack trace: the `AppError` with state 99, and stack traces from each level of the call chain. Here's `firstCall`—runtime, main, all the way down. Then we see the context we passed in: the full chain from `main` to `firstCall` to `secondCall` to `thirdCall`. You can see how each layer's context was added through wrapping.

Now if I use just `%v` without the `+`, you see only the user context. So I can choose: look at a full stack trace from any point, or just the user context. For example, `firstCall` line 51—we know the failure happened during the call to `secondCall`. Line 52—that's where the wrap happened. We have tremendous context about where we were, and we didn't have to log at every level. We didn't separate logging from error handling—it's one thing.

From a user perspective, this is all we need to remember. From a developer perspective, the rule is simple: an error occurs—do we handle it or not? If not, wrap it. If yes, then handle it: log it, decide on recovery, and stop the error from propagating.

This is a fantastic pattern. I want you to realize you don't have to log everything as an insurance policy. Signal is everything in logging—because logging has real cost. It causes allocations, consumes resources. The logs you write must be worth it—truly valuable when a problem occurs, and useful from a tracing perspective to confirm system health.

Don't forget we have dashboards and metrics. I don't like writing data into logs. I'm not a big fan of structured logging because, for me, logs exist to find and fix bugs. That's their primary purpose. For data points, I'd rather use metric systems and dashboards, and tie those together with logs when needed.

I really want you to look at Dave Cheney's package. You'll find it on GitHub under `pkg/errors`. This pattern is one we use at Ardan, and many of my clients and students use it too. It's been very, very effective for us.