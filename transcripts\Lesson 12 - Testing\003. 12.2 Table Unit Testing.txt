Table tests are a very powerful way of writing unit tests when you have a base piece of code and want to test it with a variety of different inputs and expected outputs. Instead of writing numerous separate test functions, you can leverage a single test function that uses a table—essentially a collection of input and output cases—and run the test logic across all of them. Let me show you a basic example.

We’re working with a `_test` file and have a test function for download, which takes the testing `*T` pointer. This time, notice the variable I’ve defined called `tests`. It’s a slice of anonymous structs—something we do frequently—so I don’t need to define a named type. Each struct contains an input and an expected output. In this case, the input is a URL, and the expected output is an HTTP status code.

I can populate this table with various test scenarios. For instance, one entry makes a valid call to an RSS feed, expecting a 200 OK status. Another entry calls a CNN RSS feed with a misspelled URL, which should return a 404 Not Found status. As new test cases emerge—whether from edge cases or bugs discovered in production—I don’t need to write a new test function. Instead, I simply add more entries to the table. The core test logic remains unchanged.

Before entering the actual test logic, we range over this table, extracting each input and output. You’ll see we range over `tests`, using the index `i` and the individual test case `tt`. The `t.Run` clause now indicates where we are in the data table, including the test number, which improves output clarity. Everything else proceeds as usual: we make the HTTP GET call, ensure the response is closed, and check that the returned status matches the expected one.

Now, if I want to test a 500 Internal Server Error, a 403 Forbidden, a 203 Non-Authoritative Information, or any other status code, I can simply add more entries to the table instead of writing additional test functions. Let’s run this. I’ve already navigated into the correct directory, so I’ll execute `go test` with the `-v` flag since I expect it to pass, and I want to see the detailed output.

What’s nice about the output is that you can clearly see the execution for each data point. For test zero, you see exactly what input was used and the result. Then you see the next test run with its respective input and outcome. All the checks are visible—status checks, response handling—and the verbosity gives full visibility into what’s happening. Even though this took nearly a full second to run both tests—because they execute in series—I didn’t have to write a second test function. I reused the same logic through the table.

There’s no strict format required for how your table must look. We typically use a slice of anonymous structs because it’s a clean way to group related test data—inputs and expected outputs—and then range over that collection. This pattern is common in Go, and you’ll find that many Go developers really enjoy writing table-driven tests.