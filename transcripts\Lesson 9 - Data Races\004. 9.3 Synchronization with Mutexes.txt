Now, your mutex is going to allow you to create a block of code, or multiple lines of code, and treat that as one atomic operation. This time, we're going to use the same package, and since we're making lines of code atomic, I can go back to using an int32. I've declared this mutex as a global variable here—not something we typically do in practice, but I'm showing you the mechanics. Most of the time, a mutex should be a field in a struct. Once a mutex is a field in a struct, that struct can no longer be copied, because copying a mutex creates a new mutex—this is all value semantics. Make sure you never make copies of values that contain mutexes inside them.

You’ll notice that a mutex is usable in its zero value state. It creates what we call a critical section of code that executes atomically. Let me illustrate what I mean by a critical section. I like to think of mutexes as creating rooms in your code. We can use this mutex to say: these lines of code—whatever they are—must run atomically, one after the other, with no interruption. So we put these lines of code inside the room, and the scheduler acts like a bouncer, just like at a bar. All these goroutines are people trying to enter the room; they all want in, but only one goroutine—or person—is allowed in the room at any given time. If multiple goroutines enter, we risk a data race: we can't allow simultaneous reads or modifications of the same memory location. In this case, we can't let two goroutines execute these four instructions at the same time.

The scheduler acts as the bouncer. When a goroutine arrives at the room, it asks the scheduler, "I want a lock." That lock call is blocking, and only one goroutine can acquire the lock at a time. Eventually, the scheduler says, "Okay, you goroutine right here—I'm giving you the lock. You come in." At that point, all other goroutines wait their turn. Don't assume that a goroutine arriving earlier will go first. There are fairness algorithms, but it's not deterministic. Just like at a club, someone in a fancy car might get in ahead of you. When goroutines are otherwise equal, the order isn't guaranteed—though the algorithm tries to be fair.

Once inside the room, the goroutine can execute the protected instructions and then must leave. Leaving the room is called unlock. We call unlock, which signals the scheduler to allow another goroutine to enter. Again, only one goroutine is allowed in the room at a time. The mutex enables us to create a critical section, ensuring the code executes atomically regardless of how many instructions are involved.

However, there's a real cost to using mutexes: latency. The more goroutines waiting to enter, and the longer the current goroutine takes to exit, the higher the latency. This is technically back pressure within your software. We need to measure this back pressure and reduce it. You must do the minimal amount of work that needs to be atomic. Doing more atomic work increases latency and back pressure. But you must ensure the work remains atomic—I've seen code where developers cut corners to reduce latency, only to break atomicity. I'll show you an example.

Here’s how we use the mutex. We have two goroutines and a wait group with Add(2). There are the two goroutines again, and now we see the lock and unlock calls. I do something personally that you might not see in other people’s code: I create an artificial code block using curly braces. This creates a new scope, but more importantly, it gives a visual indication in the code of which instructions are meant to execute atomically—in other words, it visually defines the room. I find this very important.

There’s another rule I’m going to insist on: the same function that calls lock must call unlock. You cannot split these calls across different functions. If you do, you risk missing a lock or unlock, leading to a deadlock. Remember, if a goroutine fails to call unlock, all other goroutines are stuck—they can’t enter the room. So lock and unlock must always be paired, always together.

Some people use defer. We haven’t discussed defer yet, but it means "execute this code after the current function returns." So the function runs, returns, then the deferred code runs. I can’t use defer here because I’m inside a loop. But you’ll often see at the beginning of a function someone call lock and defer unlock. That pattern is acceptable—especially if those are the first two lines of the function. But inside a loop, doing that would cause problems, because Go does not allow the same goroutine to call lock twice. Go keeps things simple. In languages like C++ or C#, the same thread can lock multiple times as long as it unlocks the same number of times—this is called a reentrant mutex. That doesn’t exist in Go. One lock corresponds to exactly one unlock.

Look at what we’ve done: we’ve locked three lines of code, ensuring they run atomically. There cannot be a context switch to another goroutine that also wants to enter this room. With this mutex in place, when we run example three and build it, we now get the correct result: four. If I add a fmt.Println inside the mutex to print the value—like we did before—and rebuild and run it, we still see four. But now we’ve added extra latency because the mutex must wait for the system call to complete. This is a case where I’d say: "Don’t do the print inside the mutex—you’ve just added unnecessary latency. Do only the bare minimum."

Always, inside a mutex, verify that you’re doing the absolute minimum work while still maintaining full atomicity. Everything must remain atomic. I can tell you this: if I do a code review and see something like this—which people do—it’s a major red flag. They say, "I want to get out of the mutex really quickly, so I’ll do this: create a local variable outside, like value int. Then, I’ll enter the mutex just to read the value, increment the local copy, and then lock again to write it back. Look, Bill—I’ve made my lock and unlock very fast." No—you’ve just destroyed atomicity. Even though the read is atomic, the write isn’t. Another goroutine can still read a dirty value in between. If I see a function using the same mutex with lock-unlock-lock-unlock patterns, that’s a strong code smell indicating we don’t actually have an atomic operation. These are detectable anti-patterns.

We must have one lock and one unlock, ensuring we do everything necessary—and only what’s necessary—to minimize latency. This is critical. Remember, performance is impacted by network and I/O latency, garbage collection pressure, and how we access data and hardware. These all contribute to back pressure. Holding locks too long creates algorithmic inefficiencies due to latency. But now we have the tools.

One other option is the read-write mutex. The read-write mutex operates on this principle: reading memory is safe; the problem arises only during mutation. What if ten goroutines need to read the same data simultaneously? There’s no need to synchronize reads—that would waste latency and create unnecessary back pressure. We only need to block reads when a write is pending. That’s where the read-write mutex comes in.

Here’s our read-write mutex. It allows multiple concurrent readers but only one writer at a time, and no readers while a writer is active. We have a slice of strings as our core data structure. Remember, a slice is a three-word data structure: pointer, length, and capacity. The read-write mutex is also usable in its zero value state.

On line 22, we have a read counter—a global variable to track concurrent reads. Now, look at what we do: I launch a single goroutine whose sole job is to write to this three-word data structure—in other words, perform an append. Then I launch eight goroutines that perform reads. All eight can read simultaneously—that’s fine. The problem is the write.

Here’s how it works: when we want to write, we call lock. After modifying the data, we call unlock. This tells the scheduler: "Don’t allow any other activity—reads or writes—against this data structure or these lines of code." In this case, the protected code reads the read counter and performs an append on line 71, modifying the slice.

On the reader side, each of the eight goroutines calls RLock and RUnlock. This means: "Allow these goroutines to execute the length call concurrently, because they’re only reading, not modifying." If you take an RLock, you must only read. If you write while holding an RLock, you’ll introduce synchronization bugs. We use atomic instructions for the read counter because multiple goroutines might increment it using atomic.AddInt64 on that local variable.

The scheduler handles synchronization. When a goroutine wants to write, it calls lock. At that point, it must wait for all current readers to call RUnlock. The scheduler won’t grant new read locks until the write completes. Once the write finishes and unlock is called, read locks can be granted again.

If we run example four, we’ll see this synchronization in action. We start with a slice of length zero, and you can see how many goroutines are reading the length simultaneously—up to all eight. But eventually, the writer goroutine wakes up and says, "I want to write—I want to append a value." Notice that all reads stop. The write occurs, the length becomes one, and then the reads resume. Watch the scheduler synchronize everything through the mutex: every time we write, we stop all reads, perform the modification, then allow reads again. This is a beautiful mechanism for data structures like dictionaries where you have many reads and occasional writes.

The read-write mutex is another powerful synchronization tool. However, it’s slightly slower than a regular mutex. If you need pure, strict synchronization for all operations, use a regular mutex. But if you can allow multiple concurrent reads when no writes are happening, use the read-write mutex.

So to summarize: you have atomic instructions for small data—32 or 64 bits—ideal for counters or high-performance signaling. You have mutexes to make blocks of code atomic, and you can use the same mutex across an API or multiple instruction sets in a program. But remember: mutexes create latency, and latency is death. You must do less, not more, inside mutexes. Always minimize the work done within a critical section while preserving atomicity.