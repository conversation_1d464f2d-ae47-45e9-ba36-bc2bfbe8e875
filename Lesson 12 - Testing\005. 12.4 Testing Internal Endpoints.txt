So a lot of us are building web APIs using the HTTP and HTTPS protocols, and the last thing you want to have to do when testing is stand up your own server—fortunately, you don’t have to. The `http/httptest` package gives us the ability to test all of our endpoints directly from the router or `ServeMux`, bypassing the network entirely. I want to show you how this works, and to do that, we need a basic web API. In example four, I’m using a very simple piece of code to build one. This isn’t production-level code—we’ll look at more robust implementations later, perhaps during profiling—but this is sufficient for demonstrating how to mock endpoints.

In `main`, I’ve imported a `handlers` package that contains all the routes. We’re using the default server `ServeMux`, and we’re listening on localhost port 4000. If I open the `handlers` package, you’ll see the `handlers.go` file, which includes the `routes` function we discussed. Here, we bind a route called `SendJSON` to the `SendJSON` handler function. Notice the function signature: it takes a `ResponseWriter` and a `*Request`, just like we saw with our mock—this is how handlers are written in Go. We’ve now bound this function to the `/sendjson` route on port 4000, and `ListenAndServe` will run the server.

Inside the `SendJSON` function, we define a literal struct, populate it with my name and email address, and send it over the wire as JSON. The `json.NewEncoder` writes directly to the `ResponseWriter`, encoding the data and sending it back. If I go into the example four folder, build and run the program, the server starts listening on localhost:4000. I can then navigate to `localhost:4000/sendjson`, and with just a few lines of code, I have a working web service. Again, this isn’t production-ready—it doesn’t handle load shedding or other real-world concerns—but it’s perfect for demonstrating unit testing.

Now, I want to test this endpoint to validate it works—without building, deploying, or running the server. I want to test it locally, right from my desk, starting at the route level. How do I do that? Let’s go back to the code. Since the routes are defined in the `handlers` package, I’ve created a `_test.go` file within that package—`handlers_test.go`. You can see it in my VS Code window. It contains a `TestSendJSON` function.

Notice the `init` function—it’s crucial. You must initialize the `ServeMux` and register all routes before running tests, because the test will route through the `ServeMux`. I can’t count how many times I’ve written tests only to forget binding the route. That’s why it’s important to encapsulate route registration so both your application and your tests can bind them consistently.

In `TestSendJSON`, I’m using `*testing.T`, targeting the `/sendjson` endpoint, and expecting a 200 status code. To accomplish this, I use the `net/http/httptest` package. There are two key functions here: `NewRequest` and `NewRecorder`. `NewRequest` returns a `*http.Request`—I set it up as a GET request to the `/sendjson` route with no body. `NewRecorder` returns a `*httptest.ResponseRecorder`, which implements the `http.ResponseWriter` interface—perfect, because that’s exactly what our handler expects.

Now I have both concrete types I need: a request and a response recorder. I pass them into `ServeHTTP` on the `ServeMux`. Whether you’re using the standard `ServeMux` or a third-party router, they all implement `ServeHTTP` because it’s defined by an interface. When `ServeHTTP` is called, it processes the request through the entire routing stack, executes the handler, and writes the response to the recorder.

Once the call returns, all the data I need for assertions is in the recorder. I can check the status code—was it 200? I can decode the response body into a struct and verify the name is "Bill" and the email is correct. The `http/httptest` package is brilliant. We’ve used it before to mock external servers for GET calls, and now we’re using it to mock our internal routes—testing every endpoint through the full HTTP stack, all without starting a server.

I’ll run the test now. We can also add error handling—checking for invalid inputs or edge cases throughout this code. Let’s move to the `handlers` package directory and run `go test`, filtering to execute only `TestSendJSON` with verbose output. Look how fast it runs—13 milliseconds. Everything passed. We successfully executed the endpoint, received "Bill" and the correct email in the response—exactly what we expected. But we didn’t have to deploy, configure, or manage any server. We simply registered the routes, used the `ServeMux`, passed in our request and recorder from `httptest`, and ran the entire flow.

This is a powerful integration-style test—something you’d typically see at the command or application level. Go makes it incredibly easy to test network-dependent code by letting us mock the HTTP layer and validate behavior from the route inward. It’s a robust way to ensure our endpoints work as intended, all while staying fast, isolated, and reliable.