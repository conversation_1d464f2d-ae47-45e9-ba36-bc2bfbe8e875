All right, so we just went through the operating system scheduler mechanics and the aspects of that which we're going to be sympathizing with in Go. I showed you a little bit of this when we were talking about the garbage collector. Now we're going to dive deep into how the Go scheduler works and how it sits on top of the operating system.

When your Go program starts up, it is given a logical processor that we call a P. This is modeled very much after operating systems like Linux and the concepts I showed you earlier. That logical processor P is assigned an M, which stands for machine, representing a real operating system thread—or the operating system's path of execution—which the OS is still responsible for scheduling onto some core, using all the mechanics we just discussed.

There are two data structures involved: the global run queue, and every P has its own local run queue. Recall that run queues in the operating system represent the core-level processor scheduling. Since a goroutine, like a thread, is a path of execution, a goroutine can also be in one of three states: running, runnable, or waiting. It's all the same model.

So when a goroutine starts—like the main goroutine when your Go program begins—you get that main goroutine executing in the current context. We can visualize this as the main goroutine executing on the M, within the context of this P. Maybe we draw it this way to make it clearer: our main goroutine is now running on the M because it belongs to this P.

Now, suppose this main goroutine goes ahead and creates a couple more goroutines. Those new goroutines end up in a runnable state and are placed into the local run queue. This one is executing; these others are runnable. Sometimes, a runnable goroutine might end up in the global run queue if no P has picked it up yet. This is a work-stealing scheduler—we'll talk more about that. A P can "steal" work from the global queue, bringing it into its local run queue, and then that goroutine transitions into the running state.

This is the basic model. And if you have multiple Ps—because you have multiple cores—you get only one P per core. That’s by design. The decision to have one thread per core means we don’t want to overload the operating system with more threads than necessary. At the Go level, we manage these paths of execution ourselves, and I’ll show you exactly where these mechanical sympathies lie.

With multiple Ps, you can have two goroutines running in parallel, and you can have runnable goroutines sitting in the local run queues across different Ps. You’re starting to see how this scales.

Now let’s talk about scheduling basics. The Go scheduler runs in user mode, or user space. Understand that the processor operates in two classic modes: kernel mode and user mode. Kernel mode allows executing code to do anything—it has full privileges. When you make a system call, the OS takes over and runs in kernel mode, which is why faulty drivers can crash the system: they run in kernel mode and can do whatever they want.

But when our Go code runs, it executes in user mode, which puts the processor in a protected state. The processor enforces that this code cannot perform privileged operations. If it tries, the application is faulted to protect the system.

Since the Go scheduler is built into the Go runtime—and the runtime is embedded in your application—the scheduler itself runs in user mode. And because it runs in user mode, the Go scheduler is not a preemptive scheduler but a cooperative scheduler.

Now, "cooperative scheduler" might sound like a dirty word to us old-timers who dealt with them over 20 years ago. Cooperative scheduling meant the application developer had to explicitly yield control—say, "Okay, I'm done, I'll give up my time slice." But in reality, developers were selfish: once code got CPU time, it didn’t want to give it up.

So when I hear "cooperative scheduler," I cringe—because it implies concurrency depends on developer discipline. But Go has changed the game. In Go, it’s not the developer who cooperates; it’s the Go scheduler itself that handles all the cooperation.

Because of this, the user-space cooperative scheduler in Go looks and feels preemptive—just like the OS scheduler. So while technically cooperative, it has the semantics of a preemptive scheduler. When all things are equal, its behavior is just as undeterministic as the OS scheduler. You can’t predict what it will do next.

We must internalize that the Go scheduler behaves like a preemptive scheduler. We don’t write code assuming we can predict its decisions. If we need control, we use synchronization and orchestration mechanisms—which we’ll cover later.

For the runtime scheduler to make a scheduling decision—a cooperative one—certain events must occur in the code. These events are currently triggered by function calls. (Go 1.11, currently in beta, is exploring using statements as scheduling points to avoid getting stuck in tight loops without function calls.) But as of Go 1.10, everything happens through function calls.

There are three main classes of events that allow the scheduler to make decisions:

1. The use of the `go` keyword, which creates a new goroutine—a new path of execution. Any function or method can become a goroutine by prefixing the call with `go`.

2. Garbage collection. When GC kicks in, many scheduling decisions occur because GC goroutines also need access to the available Ps—this creates significant churn.

3. System calls. These happen constantly. Any call to `log.Printf` or `fmt.Println` involves a system call. These provide frequent opportunities for the scheduler to make cooperative decisions.

More broadly, we can think of blocking calls: anything that could cause a goroutine to enter a waiting state—like acquiring a mutex or using atomic instructions. These events happen all the time, which means the scheduler, even though cooperative, can appear highly preemptive in practice.

Let’s focus on system calls, because this is where things get really interesting.

Most modern production operating systems support asynchronous system calls, and Go leverages this capability wherever possible. We use it extensively for networking and disk I/O. This is critical for efficiency.

Here’s why: suppose a goroutine on a single-threaded Go program (one P) wants to make a system call—say, open a file. That could take seconds, even "eternity" from a CPU perspective. If we let this goroutine block the OS thread (the M), then during that latency, no other work gets done. It’s a stop-the-world scenario—we can’t allow that.

So Go uses a special mechanism called the network poller, which leverages OS-level asynchronous I/O: IOCP on Windows, kqueue or epoll on Linux. The network poller includes a dedicated thread.

When a goroutine makes a system call that can be handled asynchronously, it is context-switched off the M and moved into the network poller, entering a waiting state. Its system call request is posted to the poller, and the call is handled asynchronously.

This is brilliant: we’ve freed up the M to do more work. A runnable goroutine from the local queue can now be scheduled and begin executing.

When the system call completes—when the OS or poller returns—the goroutine is moved back to the local run queue in a runnable state. Eventually, it will be scheduled again, context-switched back onto the M, and resume execution.

This is how Go maintains high efficiency: by using asynchronous system calls, we keep the number of OS threads minimal—typically one per core, plus one for the network poller. Any goroutine doing network I/O will route through this system.

But what if you’re on an OS that doesn’t support asynchronous system calls? Or you’re using cgo with a C library that inevitably blocks the thread?

In that case, the scheduler takes a different approach. This is not the normal path, and most code won’t hit it—but here’s what happens.

The goroutine wants to make a blocking system call. There’s no way to handle it asynchronously; it can’t go to the network poller. So the scheduler detaches the M and the G (goroutine) from the P, allowing them to block independently.

Now, to keep the P productive, the scheduler creates a new OS thread—M2. So now we have an extra thread in the Go program.

But here’s the key: we’re still logically a single-threaded Go application from a scheduling perspective, because the original goroutine is now running on M2 while the P continues processing its run queue with the new M.

By default, Go allows up to 10,000 such blocked threads before the program faults. This should almost never happen. If it does, it’s a red flag—reach out, I want to see it. I maybe see one such report per year on the mailing list.

Eventually, the blocking call completes. The goroutine finishes, and the thread is cached for reuse in case this happens again.

Again, this is not the normal scenario. The normal path is using the network poller to handle system calls asynchronously, keeping just one thread per core active.

This mechanism exists so that when the scheduler detects an M is about to block, it replaces it to keep the P productive.

This is a work-stealing scheduler. In a multi-P environment, if a P runs out of work, it first checks the global run queue for runnable goroutines. If there’s work there, it steals some.

It might even look at other Ps: "Hey, you’re overloaded with runnable goroutines, and I’ve got nothing to do—I’m taking one of yours." There’s a special state in the scheduler trace called "spinning" that shows when an M on a P has no work but is actively searching for goroutines.

When an M has no goroutines to run but is looking for work, it’s said to be spinning. Ideally, we want to keep Ms busy at all times. Why? Because from the OS perspective, if an M on a core transitions from running to waiting, the OS might context-switch it out, which is expensive.

The more we keep Ms busy, the more efficiently the OS scheduler treats them, and the more work we get done. This is one of the mechanical sympathies of the Go scheduler: keep the threads busy.

Let me illustrate this with a scenario that should bring it all together.

Imagine you’re writing traditional multi-threaded software—say, in C, using real OS threads. Even on a multi-core machine, suppose two threads need to pass messages back and forth. This is I/O-bound work.

No problem, you think. Wait for a context switch, get CPU time, do some work, send a message. Then you block—enter a waiting state. The other thread, notified somehow, wakes up, gets a context switch, processes the message, sends a reply, and blocks again.

Back and forth: waiting → runnable → running → waiting → runnable… Every message exchange involves multiple context switches. These threads are constantly being pulled on and off the core.

And remember: context switches are expensive.

Now imagine you’re not using OS threads directly. You’re in Go, using goroutines. Suppose you decide to run on a single P—single-threaded Go.

The chart looks similar: you still need a context switch to run. But now, the context switch happens at the M level—and it’s extremely lightweight. Why? Because the Go scheduler knows exactly what the goroutine is doing, so it saves far less state than the OS does during a thread context switch.

So goroutine context switches are not only lightweight—they’re much faster.

We get a context switch—no problem. This goroutine runs, then yields; the next runs, then yields. Same I/O pattern. But here’s the crucial difference: from the operating system’s perspective, the OS thread (the M) never enters a waiting state. It’s always either running or runnable.

The Go scheduler has effectively turned I/O-bound work into CPU-bound work.

Think about that. With CPU-bound work, having more threads than cores only adds overhead—because the cores are never idle. There’s always work to do.

Go has transformed I/O-bound concurrency into something that looks like CPU-bound work to the OS. How brilliant is that? It’s an incredible piece of engineering.

And this P abstraction removes a huge cognitive load. You don’t need to manage thread pools or goroutine pools to tune throughput and efficiency. In many cases, you can either:

- Throw a lot of work at the scheduler and let it distribute across Ps efficiently, or  
- Use a small number of goroutines—maybe one per P—and keep them busy.

There are many strategies. We’ll explore them.

But what I really wanted to show you is the mechanical sympathy of the Go scheduler—how it sits on top of the OS, leveraging but not replacing the OS scheduler.

We don’t want to rewrite OS schedulers. They’re complex, hardware-aware, NUMA-aware, and deeply optimized. The Go scheduler doesn’t compete with them—it cooperates.

The real magic is that Go turns I/O-bound work into CPU-bound work, minimizing the load on the OS scheduler. We manage concurrency and parallelism at the Go level, while the OS sees a steady, efficient stream of work.

That’s the brilliance.