So what most people in Go don't realize is that accessing a map is not inherently synchronous. You don't get synchronization for free. You don't get anything for free when it comes to synchronization and orchestration—you're responsible for it. And because so many people have encountered data races with maps, the language now has built-in runtime data race detection for map access.

Now you might think, "Whoa, whoa, whoa—that means we've added extra code to our map access to detect races at runtime?" Yes, we have. I've told you over and over again: <PERSON> cares about integrity above everything else. And there's a cost to integrity, which is performance. But the Go team has made sure that this small amount of race detection during map access won't significantly impact you. Let's take a look at how this works.

I've created a global variable called *Scores*. You shouldn't be doing this in production code—I'm doing it here to teach you some mechanics. We're using the `make` function to create a map that's usable in its zero value state. Now I've got a wait group, and based on the Add call on line 18, it looks like we're going to launch two goroutines. 

We go ahead and start a goroutine—a separate path of execution—that increments the value at key "A" in the map. Look right there: `scores["A"]++`. We're incrementing the integer associated with key "A". So eventually, that value will go from zero to one to two. At the same time, we're also incrementing the integer for key "B": one, two, three. 

We have two separate paths of execution accessing the same map concurrently and attempting modifications—even though each is working with a unique key. Because the map is a complex data structure, that doesn't matter. These are two goroutines trying to write to the same piece of data at the same time. This is a concurrent write. This is a data race.

Prior to the version of Go we're currently using, this code could have run in production, causing widespread data corruption—and we wouldn't have known until after the fact. But watch what happens now when I run this code. Let's make sure we're in example five. Okay, build—no problem. Now I run it.

Look at this: the runtime detected a concurrent map write between the two goroutines. Right here. And we also get a backtrace. Look at what it says: line 22. Let's check line 22. Hey, that's one of those map writes. The runtime is showing us exactly where the concurrent write occurred—right on line 22. 

This is powerful stuff. The runtime detects the race and ensures the program shuts down before any real damage is done to the data. I cannot stress this enough: integrity is our number one priority. When your program loses integrity, the software must go down. You either call `panic`, or you call `os.Exit`. 

This has become such an important principle in Go that the ability to detect concurrent writes to maps has been moved directly into the runtime—even if the race involves a read and a write. Any data race involving map access is now caught at runtime.

So here's to Go for being truly focused on integrity. I don't care about the small performance cost—the few clock cycles this might take—because integrity must always be our top priority.