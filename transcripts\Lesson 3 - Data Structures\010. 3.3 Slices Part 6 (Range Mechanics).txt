Now I showed you the range mechanics—the idea that a for range has both value and pointer semantics—when we were talking about arrays. But I also want to show you the behavior you can get with the same range mechanics when we're working with a slice. Let's take a look at that next.

Here it is. It's very similar to what we saw with the array, but we're only going to look at two versions of this. I'm going to add some print statements here so we can isolate the output and talk about each one individually.

Let's talk about the first one. I start with a slice of strings. We know we're pre-populating it using a literal construction: <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> being short for <PERSON>, the name of my business partner. So we have five strings: indices zero through four.

We're using a for range with value semantics, as seen on line 13. When we use the for range in value semantic mode, it means we're making a copy of the value we're iterating over—in this case, a copy of the slice value. This copy is what we're actually ranging over, hidden behind the scenes by the for range construct. This is our friends slice, and we're not ranging over the original slice; we're ranging over the copy.

We begin iterating. The first iteration gives us "Annie." Now, look at the next line of code. I'm slicing the friends slice and setting it to go from the beginning to index two, which means I'm mutating the original slice to have a length of two—only two elements—and pointing to the first two elements of the backing array.

From the perspective of the original slice, there are now only two elements. But this doesn't cause any issues because we're not iterating over the original slice. We're iterating over the copy, which still has a length of five and references all five elements. If I run this, everything works exactly as designed. The mutation to the original slice doesn't affect the loop because we're using value semantics on line 13 and iterating over our own copy.

But what if I use pointer semantics and make the same change? That's what we're doing in the second example—the pointer semantic form of the for range. While iterating, we perform the same modification: slicing the friends slice to a length of two.

Here's what happens: when the for range starts, it takes a copy of the slice header, including its length, which is five. So it expects to iterate five times. But then we mutate the original slice, reducing its length to two. Since we're using pointer semantics, we're now iterating over the original slice, not a copy. The loop logic still thinks it needs to iterate five times, but the underlying slice has been changed.

At some point, the runtime will detect that we're trying to access elements beyond the new length of the slice, which can lead to unexpected behavior or panics. This is dangerous.

The beauty of value semantics is that we're always operating on our own copy of the data. This isolation keeps us safe and allows us to perform mutations without affecting other parts of the program. Pointer semantics, on the other hand, implies shared access. When we start mutating data during iteration, we introduce the risk of unintended side effects and bugs.

I cannot stress this enough. We haven't yet covered when to use values versus pointers, but I've been trying to show you that the mechanics of value and pointer semantics are very real. Understanding them is crucial for predicting the behavior and performance of your software.