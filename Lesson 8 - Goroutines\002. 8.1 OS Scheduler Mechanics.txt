All right, we're going to start talking about concurrency, and what I want to do right now is give you the mechanical sympathies of the Go scheduler. I want you to get an understanding of where the sympathies are and why <PERSON> is going to be able to do the things that it can for us—specifically, reduce a lot of the cognitive load that we used to have when writing multi-threaded software directly with operating system threads. But Go can't eliminate all of the cognitive load. In fact, I don't think there's a single language we could invent that could do that. We're still always going to be responsible for understanding our workloads. We're always going to be responsible for what I call synchronization and orchestration. We'll start talking about what these things are.

But let's begin at the beginning and get a general, representational-level understanding of how operating system schedulers work. An operating system scheduler is what we call a preemptive scheduler, which means that when the scheduler makes decisions about how to schedule threads, we cannot predict what it will do—especially if all things are equal. These schedulers are preemptive, which essentially means non-deterministic.

When a program starts up on your operating system—regardless of whether it's Windows, Mac, or Linux—the operating system creates a process. I like to think of a process as a container for the resources that the running program will need. The word "container" carries a lot of baggage these days, so don't think about Docker or similar technologies right now. Just think of a process as a way of maintaining and managing the resources for that running program. There are many such resources, but two key ones are always top of mind: memory and threads.

First, memory: the process is given a full memory map based on the system's 32-bit or 64-bit architecture. That memory space appears real to the process, but we know it's virtual. The second key resource is a thread—specifically, the main thread. It's called the main thread because when it terminates, the entire process shuts down.

If a process is a container for resources, then think of a thread as the path of execution. That's how I like to conceptualize threads: as paths of execution. Remember, you're writing code, and that code becomes machine code, which is always executed linearly—do this instruction, then that one, then the next. Even with a jump statement, the instruction tells you exactly where to go next. So execution is always linear—a linear path. The thread's job is to manage that linear path of execution.

We have what's called an instruction pointer, or sometimes PC (program counter), which you'll see in stack traces. The PC tells us, at the hardware level, which instruction is next to execute.

So we have these paths of execution, and operating system schedulers don't care about processes—they care only about paths of execution. To keep things simple, clean, and representative, we can maintain a clear mental model without unnecessary complexity. From our perspective, a thread—or path of execution—can be in one of three states.

First, a thread can be executing, which I'll also refer to as running. This means it has been placed on a CPU core, and the next instruction in its sequence is being executed. That thread will continue executing until the operating system, in a non-deterministic way, decides it can no longer run on that core and performs a context switch.

A context switch occurs when a thread is pulled off a core and replaced with another thread. A thread can only be placed on a core if it's in a runnable state—that's the second state. So the operating system looks at all threads in the runnable state and selects the next one to run. The selection is non-deterministic. That thread then begins executing its path.

Context switches in the operating system are very expensive. The OS doesn't know what the threads are doing—it only knows they're in a state where they want to run. As a result, a tremendous amount of state information must be saved from the core when a thread is removed, so that when it's restored, the thread continues as if it had never been interrupted. This process of saving and restoring state is both critical and costly.

Additionally, each thread comes with a 1MB stack space, which also makes threads expensive.

The third state a thread can be in is the waiting state. There are many sub-states within waiting, but that's not important for our discussion. What matters is that when a thread enters the waiting state, from an execution standpoint, it effectively disappears—it's "in the matrix." It's no longer visible to the scheduler until it transitions back to the runnable state.

Threads enter the waiting state for many reasons—typically because they're waiting for something from the OS, most commonly disk I/O or network I/O. At that point, the scheduler recognizes the thread doesn't need to run and removes it from consideration. So the three states are: running, runnable, and waiting. These are the states the scheduler manages continuously, and context switching is central to this process.

In the early days, this wasn't a complicated problem because we had only one core. Imagine a single core as our execution unit. Even today, despite multiple hardware threads, from our perspective, a core can only execute one path of execution at a time. So with one core, only one thread can run at any given moment.

Back then, suppose you had 10,000 threads—10,000 paths of execution—competing for that single core. One of the operating system's jobs is to create the perception that all these threads are running simultaneously, even though physically, only one can execute at a time. To achieve this illusion, the OS scheduler gives each thread a time slice on the core.

With a single core, this isn't overly complex. The goal is to create the perception that all threads ran within a certain timeframe. If you divide that time by the number of threads, each gets a slice. Threads run, are pulled off, others run, and so on. If a thread moves from running to waiting, it uses less time, allowing others to run sooner.

Operating systems do have thread priorities, but we don't need to dive into that complexity now. The key idea is: more threads mean less time per thread, because the scheduler must maintain the illusion of concurrency. But if you have only 1,000 threads instead of 10,000, each gets more time because there are fewer to schedule.

Everything I've discussed—especially in multi-threaded software development—revolves around the idea that less is more. We must keep this in mind. More threads mean more load on the OS scheduler: more scheduling decisions, more chaos, more context switches. And during a context switch, your application code isn't running—OS code is. That time, even if nanoseconds, is latency. It's clock cycles where your code isn't executing, and it introduces inefficiency. So less is always more.

But something significant happened in 2004: multi-core processors became mainstream. You could no longer buy a server for your data center without multiple cores. This was exciting—instead of one execution unit, you now had two, meaning you could execute two threads at once, achieving true parallelism.

Concurrency means managing many things at once. In a single-core environment, the OS manages concurrency by rapidly switching between threads on one core, creating the illusion they're all running simultaneously. But with two cores, you achieve parallelism—doing multiple things at the same time. Two threads execute simultaneously.

When multi-core hardware emerged, academics began researching how OS schedulers handled this. They found threads in the runnable state while cores sat idle for nanoseconds or microseconds—threads weren't being scheduled efficiently. This was a nightmare if you wanted to leverage that second core for performance.

Multi-threaded software on multi-core processors quickly becomes complex. The scheduling problem, which had been considered solved, reopened in 2004. We had to rewrite schedulers to be smarter about multi-core architectures.

I want to show you why the scheduler isn't as efficient as we might hope, especially when it doesn't understand what threads are doing. Recall the Intel i7-900 processor we discussed earlier. If you skipped to this section because you only care about concurrency, I urge you to go back and watch the earlier material. I laid a foundation on data and mechanical sympathies that's essential for this discussion. I assume you've seen that content, especially on arrays and data locality.

We have a four-core processor. Each core has L1 and L2 cache, and there's shared L3 cache and main memory. The scheduler must understand hardware layout to be efficient. These cores communicate via physical pathways. Notice that some cores are closer than others—they can communicate faster. On larger processors, like 24-core chips, ring topologies become inefficient because the distance between cores can be large. Hardware now uses mesh topologies to minimize the maximum distance between any two cores.

Cores must communicate, and simply adding more cores doesn't guarantee better performance if you're not mechanically sympathetic to caching and processor design. The scheduler must account for all of this.

Consider this: suppose a process starts on core 1. The main thread begins executing there. As it runs, it accesses data, which loads into L1, L2, and L3 caches from main memory. Now, suppose the main thread creates a second thread—thread two—with the goal of running in parallel on a multi-core system.

But the machine is busy. Caching systems exist to reduce cache line and TLB misses. Here's a key question for an OS scheduler designer: should the scheduler preempt the main thread to let thread two run on core 1, assuming they likely share data already cached in core 1's caches? Since threads from the same process often work on related data, keeping them on the same core could be more efficient.

But what if core 2 is about to become idle? Wouldn't it be better to run thread two on core 2, even if it means cache misses and data duplication across caches? I'm not asking you to answer—my point is these are real decisions the scheduler must make. It must also consider physical proximity between cores.

Here's what I can tell you: if a core is idle, it will be used. We don't wait. We want work to execute, even if it means bringing in data. But the OS tries to create run queues—data structures that hold runnable threads. These queues can exist at the core level, processor level, or in a hierarchical tree. The OS places threads in these queues to schedule them as efficiently as possible.

Linux is brilliant at this, even on modern multi-core systems. This all happens beneath the surface. We don't have to manage it directly, but we must be mechanically sympathetic. If we're not, we won't achieve the performance we want—and we risk memory thrashing and context-switch thrashing, both of which add latency and hurt performance.

As multi-threaded software developers, we have two responsibilities: understand our workloads, and know when to use synchronization and orchestration.

Understanding workloads means recognizing two types: CPU-bound and I/O-bound.

CPU-bound workloads are those where threads never enter the waiting state—they're constantly computing, never making I/O requests. For CPU-bound work, having more threads than cores causes more harm than good because context switches add pure latency.

I/O-bound workloads involve threads moving between running, waiting, and runnable states—making database calls, disk I/O, network requests—anything that requires waiting. In these cases, having more threads per core can be beneficial. When a thread blocks, the core becomes available for another thread, keeping the CPU busy.

You must understand your workload to determine how many threads to use. You can't just throw unlimited threads at a problem.

Historically, we used thread pools to manage this. Thread pooling was our way of finding the maximum number of threads that delivered the best throughput.

I did a lot of multi-threaded development on Windows before 2013, when my company moved to Linux and I needed a new programming language. Go helped me far beyond just the OS transition.

On Windows, we used IOCP—IO Completion Ports—as our thread pooling technology. Linux has epoll, BSD has kqueue—other systems have their equivalents. IOCP was Windows' solution for thread pooling.

If I were writing a web service, I'd create an IOCP thread pool. As requests arrived, I'd post them to the pool, and a group of threads would process them.

Here's the problem: how do you size the thread pool? You need to configure it based on your hardware. Ideally, as the number of cores increases, your throughput should increase linearly. If adding cores doesn't yield linear growth—if your performance curve flattens or dips—you have a mechanical sympathy problem. Something in your algorithm, hardware usage, or system design is inefficient.

Here's what I learned about Windows: suppose I set up an IOCP thread pool with three parameters—minimum threads (say, 2), maximum threads (say, 24), and a concurrency value, which we always set to zero.

The concurrency value set to zero meant: if you have one core, allow only one active thread at a time. More active threads than cores just adds latency. But here's the puzzle: why could I set min and max thread counts higher than the number of cores if I only wanted one active thread per core?

Because you must understand your workload.

Consider this scenario: requests start arriving. Suppose we're on a two-core machine—two threads can run in parallel. We set min=2, max=24, concurrency=0. How does this work?

We start the server. Requests come in. The first request arrives—no threads are active, so the OS creates a new thread and assigns the work. We now have one active thread. A second request comes in—another thread activates. Now both cores are busy.

More requests arrive and queue up. But only two threads can be active. Work piles up. But remember, this is I/O-bound work—threads likely make database calls. When a thread makes a database request, it moves from running to waiting. Now only one thread is active.

Since we can have up to 24 threads, the OS creates a new one and assigns it work. When that thread blocks, another is created. Eventually, you might have 22 threads waiting and only two active—even on a two-core machine.

This is why workload understanding is critical. At some point, having 22 threads might overload the OS so much that performance degrades. More threads don't always mean faster processing.

I discovered an odd pattern on Windows with database-backed web services: a 3:1 ratio worked best. With one core, never more than three threads in the pool delivered the fastest completion for 100,000 requests. Two threads were slower due to idle CPU time. Four threads were slower due to excessive context switches and latency.

So if someone said performance wasn't fast enough, my solution was simple: "Give me another core." With two cores, I could use six threads. With four cores, 12 threads. Going below or above 12 on a four-core machine didn't improve performance.

Thread pools allowed us to tune based on core count and observed throughput. But this required cognitive load: we had to test, monitor, and adjust—especially as traffic patterns changed. It wasn't perfect, but it gave us consistent throughput and let us leverage hardware effectively, achieving near-linear scaling.

This stuff is complicated—and we had to deal with it on any operating system.

But this is one area where Go reduces our cognitive load. We still must understand our workloads, but we no longer have to manage thread pools ourselves. The Go scheduler handles all of that for us.

Now that we understand the OS layer—threads in running, runnable, or waiting states—and the importance of workload awareness (CPU-bound vs. I/O-bound)—and the principle that less is more—we can appreciate how these factors influence system design and performance.

The next step is to learn how the Go scheduler works—and how it sits on top of the OS to simplify these challenges for us.