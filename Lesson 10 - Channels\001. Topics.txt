Welcome to Lesson 10, Channels. This is the lesson you probably want to jump into first, and I'm going to ask you not to—please look at all of the other lessons before you get here. It's going to help you tremendously to learn about channels. Channels are how we handle orchestration in Go, and I'm going to teach you the signaling semantics around channels. I'm going to show you some very basic signaling semantics, we're going to move to some higher-level semantics, and then I'm going to show you how we bring it all together. So I'm going to give you new ways to think about channels around the idea of signaling.