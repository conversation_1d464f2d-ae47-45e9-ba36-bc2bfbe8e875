I want to show you another piece of code here around the idea of side effects and how dangerous they can become if you're not aware of them. This isn't about scaring you away from the data structure, and it's not about making you use the built-in function `copy` all the time. That's not the point at all. I just want to continue emphasizing that when you're using pointer semantics and mutating memory, you must be aware of the side effects. This is what makes programming difficult. Your functional programming language friends don't have to worry about this. Functional programming says everything uses value semantics—every piece of code, every function call operates on its own piece of data. But with that approach, we don't get the efficiencies needed to build the fastest programs we want. This is why <PERSON> is brilliant: it gives you the responsibility of both value and pointer semantics. But we have to be aware of when we're using each, and when we're using pointer semantics, we must be conscious of the mutations and the problems they can cause.

Let's look at this program. We have a user-defined type called `user`, and the idea is that we have a system of users where we track likes for each user throughout the application. Let's say we have a small system that starts up and begins running with only three users. In this program, I go ahead and create my slice—a slice of three users. This slice is set to its zero value. It's a slice of users, so I'm not sure why I'm writing strings here—funny stuff, <PERSON>. But we're just going to maintain the likes here, and we know these users start with zero likes. Let's assume we use the index position to bind each index to a user value. So we have users zero, one, and two, each with their likes set accordingly. The slice has a length and capacity of three.

Now, as I would normally do in a program, I want to share user one. I create a pointer that points to user one. It's very possible I'd do this to share that user down the call stack, perhaps in another part of the program handling likes on some event, so we can continue to increment the like count. On line 23, you see that through the `shareUser` pointer, we increment the value it points to. After line 23, user one no longer has zero likes—they now have one. Brilliant. And if you look at line 26, I've added extra print statements to isolate the output on screen. We should see that user one has one like and the others have zero. There it is: user zero has no likes, user one has one like, user two has zero likes. Perfect—exactly what I expected.

Now, the system is running and dynamic. A new user registers, and we do what we'd normally do—we append a user. But watch what happens during the append. Are length and capacity the same? Yes, they are. Uh oh. What does that mean? It means we now have to create a new backing array. The new backing array will double in size, and both length and capacity will change. Suddenly, we'll have a length of four and a capacity of six. This also means all the existing data must be copied over—which it is—and now our slice points to this new backing array.

Notice what just happened. The pointer that was pointing into the original backing array still points to that old array. But that old array is no longer our data. The new backing array is now the point of truth because of the append. That means if I continue to increment likes for the user—like you see on line 34—those increments don't get registered in the current data. You might think, "Bill, this would never happen—we're incrementing this, not that." But the reality is, we're working with the new data now. The old data is orphaned, and guess what? We effectively have a memory leak in a sense, because that old memory can't be released as long as this pointer is still referencing it.

You might say, "Bill, this would never happen," but I know some of the best Go developers on the planet who have introduced bugs exactly like this—simply not thinking through how `append` behaves when length and capacity are equal. When that happens, `append` makes a copy of the current data, which was the point of truth, and then makes the new array the point of truth. Meanwhile, any existing pointers are now operating against the old data structure.

This is a side effect. These are the nasty bugs that are incredibly hard to find. So any time we're working with pointer semantics—which is great for efficiency—we have to be careful. We also need to ensure we're very clean with data updates, especially with slices, and that our mutations aren't causing problems or happening in the wrong place. This program is just an example to show you what a side effect can really look like in production-level code, and how careful we must be when sharing slices beyond program boundaries—especially when an `append` call might happen.

One thing I do frequently is, whenever I see a call to `append` in code, I immediately pause the code review. Because `append` can create some of the nastiest side effects you've ever seen. Unless the code is decoding or unmarshaling data, an `append` call is often a bit of a code smell. It's not necessarily wrong, but it requires us to double and triple check that we're not introducing a side effect where a backing array gets replaced and existing pointers become invalid.