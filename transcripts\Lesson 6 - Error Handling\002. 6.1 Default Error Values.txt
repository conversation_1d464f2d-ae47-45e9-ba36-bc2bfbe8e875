Hi, now the next step in learning design is error handling. We started very early in one of the very first sections, when I was preparing you for this class, emphasizing that lines of code are important and that if you really want to maintain mental models and reduce the chances of legacy code, you've got to worry about failure. Failure is always what we're coding against, and error handling is everything.

It's not about exception handling, because I believe exception handling hides context around errors. This is about looking at an error when it happens. Go errors are just values—they can be anything you need them to be. But from an API design perspective, error handling is about one thing: showing respect to the user of your API. It means giving your user enough context to make an informed decision about the state of the application and providing them with sufficient information to either recover or decide to shut down.

If your application loses integrity, you have a responsibility to shut it down. I don't want to shut down apps, but you have a responsibility—because of integrity, data corruption—no, no, no, no, no, no, no, no, no. There are two ways to shut down an application in Go. You can go to the os package, call os.Exit, and set a return code—that's the fastest way—or you can call the built-in function panic. You'll choose one over the other depending on whether you need a stack trace. If you need the stack trace, you'll call panic; if you don't, you just call os.Exit.

But we must design APIs and errors around the idea of giving the user enough context to make an informed decision. So let's start with the basic mechanics and what the language provides in terms of error handling.

What you're seeing here is the error interface. This interface is built into the language. There isn't actually a package called "builtin"—that name exists purely for documentation purposes. Look at the error interface: it appears unexported, but it isn't, because it's part of the language specification. It has one method: Error, which returns a string. This interface is how we work with errors.

Error handling in Go is done from a decoupled state, and that's very important. Because error handling is widespread, we want it decoupled so we can change, improve, and refactor error handling without creating a cascade of changes throughout the codebase. This interface is critical.

Again, errors are just values in Go. The most widely used value for error handling is what you see on line 15: the errorString type, which comes from the errors package. Notice a couple of things about this type: it's an unexported struct, and it has one field—an unexported string field named S. This is the most commonly used error value in Go, and in many cases, it gives us enough context to allow the caller to make an informed decision.

The next thing you'll see from the errors package is the implementation of the error interface. I want you to notice two things: first, the use of pointer semantics, which is very important—we'll come back to explain why. Unless you're working with an error value type that inherently requires value semantics, such as a reference type or a built-in type, if you're using a struct, you almost always want pointer semantics for implementing the error interface.

Notice that this implementation serves one primary purpose: to make the value compliant with the error interface, and secondarily, for logging. Whatever comes out of the Error method should be used for logging and logging only. If your user has to parse the string to extract context, you have failed the user. We cannot require string parsing to obtain error context.

Now that we have the interface implementation, we have the factory function—there it is, again from the errors package. This is how we create errors. Importantly, look at the return type of this factory: it's the error interface. On line 27, we construct a value of the unexported errorString type, take its address (because we're using pointer semantics), and assign it to the error interface.

Any time you call errors.New, I want to show you on the whiteboard what that error variable looks like. What's interesting is that this factory function doesn't return a concrete value directly—it returns an interface value. Remember, I've said factory functions should usually return concrete values or pointers, but this is the one exception: errors.New. You always work with errors from an interface perspective.

When you call errors.New, what you get back is an error interface value. It says, "I have a pointer to an errorString," and it holds a pointer to the struct, which is essentially just a string. It probably wouldn't say "Bill"—it would be some error message. This is what we get back: we work with errors in a decoupled state. It's an error interface value containing a concrete pointer to our error message.

It's very typical in Go to see code like this for error handling. Notice we're in an if statement: we make a call—say, webCall—and check if webCall failed by testing error != nil.

There are a few things I want to discuss here. People liked exception handling because the idea was that the try block contained the happy path and the catch block handled all the negative paths, separating them for code readability. I get that. But I can't tell you how many times, when I landed in a catch block, I was lost—because the context of how I got there was gone. Was it this call? That call? I spent too much time figuring out how I ended up in the catch block. It doesn't work.

But you can still achieve the concept of a happy path in Go by following a basic design principle: when it's reasonable and practical, use the if statement to handle the negative path logic and keep the positive path logic on a straight line of sight. In other words, if the call to webCall succeeds, you just move down the function to the next call.

That straight line—the indented path—is your happy path. You get the same readability benefits as with exception handling. If the call succeeds, no problem: you maintain your line of sight and continue down this path. Use the if statement to handle the negative path.

The indented block is your negative path, and the idea is to return out of it. If there's a failure, handle it—whatever that means—and then return. Even though a function has a single entry point, get used to the idea that it can have multiple exit points.

One thing I strongly advise avoiding is the else clause. Else clauses make code an order of magnitude more complicated to read. What they usually do is put positive path logic inside the if block and try to push the function from start to finish, which forces me to read and understand more code as I go through the function.

When you use this pattern, I don't have to worry about reading as much code. If I've reached line 36, I know everything above has already succeeded and is fine. So I really want you to follow this pattern. Avoid else clauses because we're not aiming for a single exit point.

What's also nice in Go is the concept of a "naked" switch. You can use Boolean logic in a switch statement. If you find yourself with an if-else-if chain, use a switch instead, and again, leverage the idea of handling the negative path with early returns. The negative path should always be indented.

Try these scopes—these paths—you'll find they really help. As we continue working with code, I'll show you how this codebase implements this pattern.

Great. So we have the call to webCall. webCall returns an error—an error interface value—where the string inside says "bad request." When we call webCall, we take that error interface value, store it in a local variable named error using short variable declaration, which binds it only to the if statement. This is great. Then we check error != nil.

This is interesting because nil is a unique concept in Go. Nil is the zero value for types that can be nil: pointers and reference types. Nil always takes on the type it needs to satisfy an expression. In this case, since err is an error interface value, nil represents a nil error interface value. It takes on the required type so the comparison can happen.

I like to think in English: "error != nil" really asks one question: "Is there a concrete value stored inside this error interface?" Here's a case where there's no concrete value; here's one where there is. In our case, there is one.

So whenever I see error != nil, what we're really asking is: Is there a value—a piece of data, a value or a pointer—stored concretely inside the error interface? If there is, that means we have an error.

In this case, the mere presence of an error is all the context we need, because this function returns only one possible error. We know what that error is, and we have enough context to make an informed decision about how to handle it. Right now, I'm just logging it and returning it.

So when a function returns just one error, often the fact that an error exists—that there's a concrete value in the error interface—is sufficient.

But what if webCall could return multiple errors? Then we'd need a different mechanism to give the user enough context to make an informed decision.